import { renderHook, act } from '@testing-library/react';
import useFormValidation from '../../../hooks/useFormValidation';

describe('useFormValidation - Simple Tests', () => {
  const mockInitialValues = {
    email: '',
    name: '',
    mobile: '',
    agreeToTerms: false
  };

  describe('Hook Initialization', () => {
    it('initializes with default values', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      expect(result.current.values).toEqual(mockInitialValues);
      expect(result.current.errors).toEqual({});
      expect(result.current.touched).toEqual({});
      expect(typeof result.current.isValid).toBe('boolean');
    });

    it('renders without errors', () => {
      expect(() => {
        renderHook(() => useFormValidation(mockInitialValues));
      }).not.toThrow();
    });

    it('provides all expected methods', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      expect(typeof result.current.handleInputChange).toBe('function');
      expect(typeof result.current.handleBlur).toBe('function');
      expect(typeof result.current.validateForm).toBe('function');
      expect(typeof result.current.resetForm).toBe('function');
      expect(typeof result.current.setFieldValue).toBe('function');
      expect(typeof result.current.setFieldError).toBe('function');
      expect(typeof result.current.setFieldTouched).toBe('function');
    });
  });

  describe('Field Value Management', () => {
    it('updates field values using handleInputChange', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
      });

      expect(result.current.values.email).toBe('<EMAIL>');
    });

    it('updates field values using setFieldValue', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.setFieldValue('name', 'John Doe');
      });

      expect(result.current.values.name).toBe('John Doe');
    });

    it('clears errors when field value changes', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      // First set an error
      act(() => {
        result.current.setFieldError('email', 'Invalid email');
      });

      expect(result.current.errors.email).toBe('Invalid email');

      // Then change the field value
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
      });

      expect(result.current.errors.email).toBeUndefined();
    });
  });

  describe('Form State Management', () => {
    it('resets form to initial values', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      // Change values and add errors
      act(() => {
        result.current.setFieldValue('email', '<EMAIL>');
        result.current.setFieldError('name', 'Some error');
        result.current.setFieldTouched('email', true);
      });

      expect(result.current.values.email).toBe('<EMAIL>');
      expect(result.current.errors.name).toBe('Some error');
      expect(result.current.touched.email).toBe(true);

      // Reset form
      act(() => {
        result.current.resetForm();
      });

      expect(result.current.values).toEqual(mockInitialValues);
      expect(result.current.errors).toEqual({});
      expect(result.current.touched).toEqual({});
    });

    it('manages touched state correctly', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleBlur('email');
      });

      expect(result.current.touched.email).toBe(true);

      act(() => {
        result.current.setFieldTouched('name', true);
      });

      expect(result.current.touched.name).toBe(true);
    });

    it('manages error state correctly', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.setFieldError('email', 'Custom error');
      });

      expect(result.current.errors.email).toBe('Custom error');
      expect(result.current.isValid).toBe(false);
    });

    it('calculates isValid correctly', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      // Initially valid (no errors)
      expect(result.current.isValid).toBe(true);

      // Add error
      act(() => {
        result.current.setFieldError('email', 'Error');
      });

      expect(result.current.isValid).toBe(false);

      // Clear errors
      act(() => {
        result.current.resetForm();
      });

      expect(result.current.isValid).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('handles empty initial values', () => {
      const { result } = renderHook(() => 
        useFormValidation({})
      );

      expect(result.current.values).toEqual({});
      expect(result.current.isValid).toBe(true);
    });

    it('handles undefined field operations', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleBlur('unknownField');
      });

      expect(result.current.touched.unknownField).toBe(true);
    });

    it('handles null and undefined values', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.setFieldValue('name', null);
      });

      expect(result.current.values.name).toBe(null);
    });
  });

  describe('Hook Stability', () => {
    it('maintains consistent structure across re-renders', () => {
      const { result, rerender } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      const initialMethods = {
        handleInputChange: result.current.handleInputChange,
        handleBlur: result.current.handleBlur,
        validateForm: result.current.validateForm,
        resetForm: result.current.resetForm
      };

      rerender();

      expect(typeof result.current.handleInputChange).toBe('function');
      expect(typeof result.current.handleBlur).toBe('function');
      expect(typeof result.current.validateForm).toBe('function');
      expect(typeof result.current.resetForm).toBe('function');
    });

    it('handles multiple rapid state changes', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.setFieldValue('email', '<EMAIL>');
        result.current.setFieldValue('email', '<EMAIL>');
        result.current.setFieldValue('email', '<EMAIL>');
      });

      expect(result.current.values.email).toBe('<EMAIL>');
    });
  });

  describe('Performance', () => {
    it('handles form operations efficiently', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      const startTime = Date.now();
      
      act(() => {
        for (let i = 0; i < 10; i++) {
          result.current.setFieldValue('email', `test${i}@example.com`);
        }
      });

      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(100);
    });
  });
});
