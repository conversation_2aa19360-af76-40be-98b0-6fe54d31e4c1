# SonarQube Setup Guide

This guide explains how to set up and use SonarQube with <PERSON>er Compose for static code analysis and test coverage reporting in this React project.

## 📋 Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- Node.js and npm installed
- Jest testing framework configured (already done)

## 🚀 Quick Start

### 1. Start SonarQube
```bash
npm run sonar:start
```

### 2. Run Unit Tests with Coverage
```bash
npm run test:unit
```

### 3. Run SonarQube Analysis
```bash
npm run sonar:scan
```

### 4. View Results
Open your browser and navigate to: **http://localhost:9000**

## 📁 Files Created

### `docker-compose.sonarqube.yml`
Docker Compose configuration for SonarQube community edition:
- Exposes port 9000 for web UI
- Disables bootstrap checks to prevent memory issues
- Uses `sonarqube:community` image

### `sonar-project.properties`
SonarQube project configuration:
- Project key: `ai-react-frontend`
- Source directory: `src`
- Test inclusions: All test files (*.test.*, *.spec.*)
- Coverage report path: `coverage/lcov.info`

### `run-sonar.cmd`
Windows batch script for complete workflow automation:
- Starts SonarQube container
- Waits for startup
- Runs unit tests with coverage
- Executes SonarQube analysis
- Provides status feedback

## 🛠 Available Scripts

| Script | Command | Description |
|--------|---------|-------------|
| `npm run sonar:start` | Start SonarQube container |
| `npm run sonar:stop` | Stop SonarQube container |
| `npm run sonar:clean` | Stop and remove container |
| `npm run sonar:scan` | Run SonarQube analysis |
| `npm run test:unit` | Run Jest tests with coverage |

## 📊 Coverage Reports

The setup generates coverage reports in multiple formats:
- **LCOV format**: `coverage/lcov.info` (for SonarQube)
- **HTML report**: `coverage/lcov-report/index.html` (for local viewing)
- **JSON format**: `coverage/coverage-final.json`

## 🔧 Configuration Details

### Jest Configuration
- Test environment: jsdom
- Coverage collection from specific components
- Excludes mock files and test files from coverage
- Supports TypeScript and JSX

### SonarQube Configuration
- Project key: `ai-react-frontend`
- Source encoding: UTF-8
- JavaScript LCOV report path configured
- Test file patterns defined

## 🚨 Troubleshooting

### Docker Issues
If Docker commands fail:
1. Ensure Docker Desktop is running
2. Check Docker installation: `docker --version`
3. Verify Docker Compose: `docker compose version`

### SonarQube Access Issues
If http://localhost:9000 is not accessible:
1. Wait 30-60 seconds after starting (SonarQube takes time to initialize)
2. Check container status: `docker ps`
3. Check container logs: `docker logs sonarqube`

### Test Coverage Issues
If coverage reports are not generated:
1. Ensure Jest is properly configured
2. Check test files are being found
3. Verify coverage directory exists

## 📈 Usage Workflow

### Complete Analysis Workflow
```bash
# 1. Start SonarQube
npm run sonar:start

# 2. Wait for SonarQube to be ready (30-60 seconds)

# 3. Run tests with coverage
npm run test:unit

# 4. Run SonarQube analysis
npm run sonar:scan

# 5. View results at http://localhost:9000
```

### Automated Workflow (Windows)
```bash
# Run the complete workflow with one command
./run-sonar.cmd
```

## 🎯 Expected Results

After successful setup, you should see:
- ✅ SonarQube web interface at http://localhost:9000
- ✅ Project `ai-react-frontend` in SonarQube dashboard
- ✅ Code coverage metrics displayed
- ✅ Code quality analysis results
- ✅ Test results integration

## 🔄 Maintenance

### Stopping SonarQube
```bash
npm run sonar:stop
```

### Cleaning Up
```bash
npm run sonar:clean
```

### Updating Analysis
Re-run the analysis anytime after code changes:
```bash
npm run test:unit && npm run sonar:scan
```

## 📝 Notes

- SonarQube data persists between container restarts
- First startup may take longer as SonarQube initializes
- Coverage reports are regenerated on each test run
- Analysis results are updated on each scan
