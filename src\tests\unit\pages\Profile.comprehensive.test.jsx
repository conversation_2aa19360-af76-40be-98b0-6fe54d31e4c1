import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import Profile from '../../../pages/Profile';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const { toast } = require('sonner');

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, sx, component, ...props }) => <div data-testid="box">{children}</div>,
  Container: ({ children, maxWidth, sx }) => <div data-testid="container">{children}</div>,
  Paper: ({ children, sx }) => <div data-testid="paper">{children}</div>,
  Typography: ({ children, variant, sx, color }) => (
    <div data-testid={`typography-${variant}`}>{children}</div>
  ),
  TextField: ({
    label,
    value,
    onChange,
    disabled,
    fullWidth,
    InputProps,
    size,
    error,
    helperText,
    ...props
  }) => (
    <div>
      <label>{label}</label>
      <input
        data-testid={`input-${label.toLowerCase().replace(/\s+/g, '-')}`}
        value={value}
        onChange={onChange}
        disabled={disabled}
      />
    </div>
  ),
  Button: ({ children, onClick, disabled, variant, size, sx }) => (
    <button 
      onClick={onClick}
      disabled={disabled}
      data-testid={`button-${variant || 'default'}`}
    >
      {children}
    </button>
  ),
  Avatar: ({ children, sx }) => <div data-testid="avatar">{children}</div>,
  Grid: ({ children, container, item, xs, md, sm, spacing }) => (
    <div data-testid={container ? 'grid-container' : 'grid-item'}>{children}</div>
  ),
  Card: ({ children, sx }) => <div data-testid="card">{children}</div>,
  CardContent: ({ children, sx }) => <div data-testid="card-content">{children}</div>,
  Divider: ({ sx }) => <hr data-testid="divider" />,
  AppBar: ({ children, position, sx }) => <div data-testid="app-bar">{children}</div>,
  Toolbar: ({ children }) => <div data-testid="toolbar">{children}</div>,
  IconButton: ({ children, onClick, edge, sx }) => (
    <button onClick={onClick} data-testid="icon-button">{children}</button>
  ),
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  ArrowLeft: ({ size }) => <span data-testid="arrow-left-icon">←</span>,
  User: ({ size, style }) => <span data-testid="user-icon">👤</span>,
  Mail: ({ size, style }) => <span data-testid="mail-icon">📧</span>,
  Phone: ({ size, style }) => <span data-testid="phone-icon">📞</span>,
  Calendar: ({ size, style }) => <span data-testid="calendar-icon">📅</span>,
}));

// Wrapper component for router
const ProfileWrapper = () => (
  <BrowserRouter>
    <Profile />
  </BrowserRouter>
);

describe('Profile - Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe('Initial Render', () => {
    it('renders profile page with all elements', () => {
      render(<ProfileWrapper />);
      
      expect(screen.getByTestId('app-bar')).toBeInTheDocument();
      expect(screen.getByTestId('toolbar')).toBeInTheDocument();
      expect(screen.getByTestId('icon-button')).toBeInTheDocument();
      expect(screen.getByTestId('arrow-left-icon')).toBeInTheDocument();
      expect(screen.getByText('Profile Settings')).toBeInTheDocument();
      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getAllByTestId('card')).toHaveLength(2);
      expect(screen.getByTestId('avatar')).toBeInTheDocument();
    });

    it('displays user information correctly', () => {
      render(<ProfileWrapper />);
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText(/Member since/)).toBeInTheDocument();
      expect(screen.getByText('Personal Information')).toBeInTheDocument();
    });

    it('displays avatar with user initials', () => {
      render(<ProfileWrapper />);
      
      const avatar = screen.getByTestId('avatar');
      expect(avatar).toHaveTextContent('JD');
    });

    it('displays form fields with correct labels', () => {
      render(<ProfileWrapper />);

      expect(screen.getByText('Full Name')).toBeInTheDocument();
      expect(screen.getByText('Email Address')).toBeInTheDocument();
      expect(screen.getByText('Phone Number')).toBeInTheDocument();
      expect(screen.getByText('Join Date')).toBeInTheDocument();
    });

    it('displays form fields with correct values', () => {
      render(<ProfileWrapper />);
      
      expect(screen.getByTestId('input-full-name')).toHaveValue('John Doe');
      expect(screen.getByTestId('input-email-address')).toHaveValue('<EMAIL>');
      expect(screen.getByTestId('input-phone-number')).toHaveValue('+****************');
      expect(screen.getByTestId('input-join-date')).toHaveValue('1/15/2024');
    });

    it('displays form inputs correctly', () => {
      render(<ProfileWrapper />);

      expect(screen.getByTestId('input-full-name')).toBeInTheDocument();
      expect(screen.getByTestId('input-email-address')).toBeInTheDocument();
      expect(screen.getByTestId('input-phone-number')).toBeInTheDocument();
      expect(screen.getByTestId('input-join-date')).toBeInTheDocument();
    });

    it('shows edit profile button initially', () => {
      render(<ProfileWrapper />);
      
      expect(screen.getByText('Edit Profile')).toBeInTheDocument();
      expect(screen.queryByText('Cancel')).not.toBeInTheDocument();
      expect(screen.queryByText('Save')).not.toBeInTheDocument();
    });

    it('has form fields disabled initially', () => {
      render(<ProfileWrapper />);
      
      expect(screen.getByTestId('input-full-name')).toBeDisabled();
      expect(screen.getByTestId('input-email-address')).toBeDisabled();
      expect(screen.getByTestId('input-phone-number')).toBeDisabled();
      expect(screen.getByTestId('input-join-date')).toBeDisabled();
    });
  });

  describe('Navigation', () => {
    it('navigates to dashboard when back button is clicked', () => {
      render(<ProfileWrapper />);
      
      const backButton = screen.getByTestId('icon-button');
      fireEvent.click(backButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });

    it('handles multiple clicks on back button', () => {
      render(<ProfileWrapper />);
      
      const backButton = screen.getByTestId('icon-button');
      fireEvent.click(backButton);
      fireEvent.click(backButton);
      
      expect(mockNavigate).toHaveBeenCalledTimes(2);
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });
  });

  describe('Edit Mode', () => {
    it('enters edit mode when edit button is clicked', () => {
      render(<ProfileWrapper />);
      
      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);
      
      expect(screen.queryByText('Edit Profile')).not.toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('Save')).toBeInTheDocument();
    });

    it('enables form fields in edit mode', () => {
      render(<ProfileWrapper />);
      
      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);
      
      expect(screen.getByTestId('input-full-name')).not.toBeDisabled();
      expect(screen.getByTestId('input-email-address')).not.toBeDisabled();
      expect(screen.getByTestId('input-phone-number')).not.toBeDisabled();
      // Join date should remain disabled
      expect(screen.getByTestId('input-join-date')).toBeDisabled();
    });

    it('cancels edit mode when cancel button is clicked', () => {
      render(<ProfileWrapper />);
      
      // Enter edit mode
      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);
      
      // Make some changes
      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'Jane Doe' } });
      
      // Cancel changes
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      
      expect(screen.getByText('Edit Profile')).toBeInTheDocument();
      expect(screen.queryByText('Cancel')).not.toBeInTheDocument();
      expect(screen.queryByText('Save')).not.toBeInTheDocument();
      expect(screen.getByTestId('input-full-name')).toHaveValue('John Doe'); // Should revert
    });
  });

  describe('Form Input Handling', () => {
    beforeEach(() => {
      render(<ProfileWrapper />);
      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);
    });

    it('updates name field when changed', () => {
      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'Jane Smith' } });

      expect(nameInput).toHaveValue('Jane Smith');
    });

    it('updates email field when changed', () => {
      const emailInput = screen.getByTestId('input-email-address');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(emailInput).toHaveValue('<EMAIL>');
    });

    it('updates phone field when changed', () => {
      const phoneInput = screen.getByTestId('input-phone-number');
      fireEvent.change(phoneInput, { target: { value: '+****************' } });

      expect(phoneInput).toHaveValue('+****************');
    });

    it('handles empty input values', () => {
      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: '' } });

      expect(nameInput).toHaveValue('');
    });

    it('handles special characters in input', () => {
      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'José María O\'Connor-Smith' } });

      expect(nameInput).toHaveValue('José María O\'Connor-Smith');
    });
  });



  describe('Data Persistence', () => {
    it('preserves original data when canceling after changes', () => {
      render(<ProfileWrapper />);

      // Enter edit mode and make changes
      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);

      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'Changed Name' } });

      // Cancel changes
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      // Re-enter edit mode to verify data is restored
      const editButtonAgain = screen.getByText('Edit Profile');
      fireEvent.click(editButtonAgain);

      expect(screen.getByTestId('input-full-name')).toHaveValue('John Doe');
    });




  });

  describe('Date Formatting', () => {
    it('displays join date in correct format', () => {
      render(<ProfileWrapper />);

      const joinDateInput = screen.getByTestId('input-join-date');
      expect(joinDateInput).toHaveValue('1/15/2024');
    });

    it('displays member since text correctly', () => {
      render(<ProfileWrapper />);

      expect(screen.getByText(/Member since 1\/15\/2024/)).toBeInTheDocument();
    });
  });

  describe('UI State Management', () => {
    it('maintains edit state during input changes', () => {
      render(<ProfileWrapper />);

      // Enter edit mode
      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);

      // Make changes
      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'New Name' } });

      // Should still be in edit mode
      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('Save')).toBeInTheDocument();
      expect(screen.queryByText('Edit Profile')).not.toBeInTheDocument();
    });



    it('handles rapid button clicks gracefully', () => {
      render(<ProfileWrapper />);

      const editButton = screen.getByText('Edit Profile');

      // Click multiple times rapidly
      fireEvent.click(editButton);
      fireEvent.click(editButton);
      fireEvent.click(editButton);

      // Should only enter edit mode once
      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('Save')).toBeInTheDocument();
      expect(screen.queryByText('Edit Profile')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {







  });

  describe('Accessibility', () => {
    it('has proper form labels', () => {
      render(<ProfileWrapper />);

      expect(screen.getByText('Full Name')).toBeInTheDocument();
      expect(screen.getByText('Email Address')).toBeInTheDocument();
      expect(screen.getByText('Phone Number')).toBeInTheDocument();
      expect(screen.getByText('Join Date')).toBeInTheDocument();
    });

    it('has accessible button text', () => {
      render(<ProfileWrapper />);

      expect(screen.getByText('Edit Profile')).toBeInTheDocument();

      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);

      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('Save')).toBeInTheDocument();
    });


  });
});
