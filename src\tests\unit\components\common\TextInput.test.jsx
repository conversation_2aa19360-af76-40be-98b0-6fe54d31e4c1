import React from 'react';
import { render, screen } from '@testing-library/react';
import TextInput from '@/components/common/TextInput';

jest.mock('@mui/material', () => ({
  TextField: jest.fn(({ InputProps, fullWidth, label, type, value, onChange, onBlur, error, helperText, disabled, autoComplete, placeholder, size, sx, loading, ...props }) => {
    // Filter out non-DOM props but keep data-* and aria-* attributes
    const domProps = Object.keys(props).reduce((acc, key) => {
      if (key.startsWith('data-') || key.startsWith('aria-')) {
        acc[key] = props[key];
      }
      return acc;
    }, {});

    return (
      <div
        data-testid="mui-textfield"
        data-fullwidth={fullWidth}
        data-label={label}
        data-type={type}
        data-error={error}
        data-disabled={disabled}
        data-size={size}
        {...domProps}
      >
        {InputProps?.startAdornment}
        {InputProps?.endAdornment}
        {helperText && <span data-testid="helper-text">{helperText}</span>}
      </div>
    );
  }),
  InputAdornment: jest.fn(({ children, position, ...props }) => <span data-testid={`adornment-${position}`}>{children}</span>),
}));

describe('TextInput', () => {
  const requiredProps = {
    label: 'Text',
    value: '',
    onChange: jest.fn(),
  };

  it('renders with required props', () => {
    render(<TextInput {...requiredProps} />);
    expect(screen.getByTestId('mui-textfield')).toBeInTheDocument();
  });

  it('shows startAdornment when provided', () => {
    render(<TextInput {...requiredProps} startAdornment={<span data-testid="custom-start">S</span>} />);
    expect(screen.getByTestId('adornment-start')).toContainElement(screen.getByTestId('custom-start'));
  });

  it('shows endAdornment when provided', () => {
    render(<TextInput {...requiredProps} endAdornment={<span data-testid="custom-end">E</span>} />);
    expect(screen.getByTestId('adornment-end')).toContainElement(screen.getByTestId('custom-end'));
  });

  it('does not show adornments when not provided', () => {
    render(<TextInput {...requiredProps} />);
    expect(screen.queryByTestId('adornment-start')).toBeNull();
    expect(screen.queryByTestId('adornment-end')).toBeNull();
  });

  it('forwards extra props to TextField', () => {
    render(<TextInput {...requiredProps} data-custom="foo" />);
    expect(screen.getByTestId('mui-textfield')).toHaveAttribute('data-custom', 'foo');
  });

  it('renders with helperText and error', () => {
    const { TextField } = require('@mui/material');
    render(<TextInput {...requiredProps} helperText="Error!" error={true} />);
    const lastCall = TextField.mock.calls[TextField.mock.calls.length - 1][0];
    expect(lastCall.helperText).toBe('Error!');
    expect(lastCall.error).toBe(true);
  });

  it('renders with custom label, type, and placeholder', () => {
    render(<TextInput {...requiredProps} label="Custom" type="email" placeholder="Enter" />);
    const { TextField } = require('@mui/material');
    const lastCall = TextField.mock.calls[TextField.mock.calls.length - 1][0];
    expect(lastCall.label).toBe('Custom');
    expect(lastCall.type).toBe('email');
    expect(lastCall.placeholder).toBe('Enter');
  });
}); 