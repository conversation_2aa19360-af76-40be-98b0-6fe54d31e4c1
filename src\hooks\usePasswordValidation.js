import { useState, useMemo } from 'react';
import { AUTH_RULES, AUTH_MESSAGES } from '../constants/validationMessages';

const usePasswordValidation = (password = '', confirmPassword = '', touched = {}) => {
  const passwordErrors = useMemo(() => {
    const errors = [];
    if (!password) {
      errors.push(AUTH_MESSAGES.REQUIRED);
    } else {
      AUTH_RULES.forEach(rule => {
        if (!rule.regex.test(password)) {
          errors.push(rule.message);
        }
      });
    }
    return errors;
  }, [password]);

  const confirmPasswordError = useMemo(() => {
    if (!confirmPassword && touched.confirmPassword) {
      return AUTH_MESSAGES.CONFIRM_REQUIRED;
    } else if (confirmPassword && password !== confirmPassword) {
      return AUTH_MESSAGES.MISMATCH;
    }
    return '';
  }, [confirmPassword, password, touched.confirmPassword]);

  const passwordsMatch = useMemo(() => {
    return password && confirmPassword && password === confirmPassword;
  }, [password, confirmPassword]);

  const isPasswordValid = useMemo(() => {
    return passwordErrors.length === 0 && password.length > 0;
  }, [passwordErrors, password]);

  const isValid = useMemo(() => {
    return passwordsMatch && isPasswordValid;
  }, [passwordsMatch, isPasswordValid]);

  return {
    passwordErrors,
    confirmPasswordError,
    passwordsMatch,
    isPasswordValid,
    isValid,
    hasPasswordError: touched.password && passwordErrors.length > 0,
    hasConfirmPasswordError: touched.confirmPassword && !!confirmPasswordError,
  };
};

export default usePasswordValidation;
