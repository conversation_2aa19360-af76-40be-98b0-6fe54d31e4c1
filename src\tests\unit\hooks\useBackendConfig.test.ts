import { renderHook, act, waitFor } from '@testing-library/react';
import { useBackendConfig } from '@/hooks/useBackendConfig';
import { configService } from '@/services/configService';

// Utility to flush all pending promises
const flushPromises = () => new Promise(resolve => setTimeout(resolve, 0));

// Custom utility to handle async hook testing with proper act() wrapping
const renderAsyncHook = async (hookFn: () => any) => {
  let hookResult: any;

  await act(async () => {
    hookResult = renderHook(hookFn);
    // Flush all pending promises to ensure async operations complete
    await flushPromises();
  });

  return hookResult;
};

// Mock the configService
jest.mock('@/services/configService', () => ({
  configService: {
    getConfig: jest.fn(),
    loadConfig: jest.fn(),
  },
}));

const mockConfigService = configService as jest.Mocked<typeof configService>;

describe('useBackendConfig', () => {
  // Note: Some act() warnings may appear for async operations in useEffect.
  // These are expected due to the nature of testing hooks with async effects.
  const mockConfig = {
    currentBackend: 'spring' as const,
    backendUrl: 'http://localhost:8080',
    environment: 'development',
    serviceName: 'test-service',
    namespace: 'default',
    apiVersion: 'v1',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Initial Loading', () => {
    it('should start with loading state', () => {
      // Arrange
      mockConfigService.getConfig.mockImplementation(() => new Promise(() => {})); // Never resolves

      // Act
      const { result } = renderHook(() => useBackendConfig());

      // Assert
      expect(result.current.loading).toBe(true);
      expect(result.current.config).toBe(null);
      expect(result.current.error).toBe(null);
    });

    it('should load config successfully', async () => {
      // Arrange
      mockConfigService.getConfig.mockResolvedValue(mockConfig);

      // Act - use custom utility to properly handle async hook
      const { result } = await renderAsyncHook(() => useBackendConfig());

      // Assert - wait for async operations to complete
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.config).toEqual(mockConfig);
      expect(result.current.error).toBe(null);
      expect(result.current.currentBackend).toBe('spring');
      expect(result.current.backendUrl).toBe('http://localhost:8080');
    });

    it('should handle config loading error', async () => {
      // Arrange
      const errorMessage = 'Failed to load config';
      mockConfigService.getConfig.mockRejectedValue(new Error(errorMessage));

      // Act - use custom utility to properly handle async hook
      const { result } = await renderAsyncHook(() => useBackendConfig());

      // Assert - wait for async operations to complete
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.config).toBe(null);
      expect(result.current.error).toBe(errorMessage);
      expect(result.current.currentBackend).toBe('spring'); // fallback
      expect(result.current.backendUrl).toBe('http://localhost:8080'); // fallback
    });

    it('should handle non-Error exceptions', async () => {
      // Arrange
      mockConfigService.getConfig.mockRejectedValue('String error');

      // Act - use custom utility to properly handle async hook
      const { result } = await renderAsyncHook(() => useBackendConfig());

      // Assert - wait for async operations to complete
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error).toBe('Failed to load configuration');
    });
  });

  describe('Config Refresh', () => {
    it('should refresh config successfully', async () => {
      // Arrange
      mockConfigService.getConfig.mockResolvedValue(mockConfig);
      const updatedConfig = { ...mockConfig, currentBackend: 'nest' as const };
      mockConfigService.loadConfig.mockResolvedValue(updatedConfig);

      // Act - use custom utility to properly handle async hook
      const { result } = await renderAsyncHook(() => useBackendConfig());

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Act
      await act(async () => {
        await result.current.refreshConfig();
      });

      // Assert
      expect(result.current.config).toEqual(updatedConfig);
      expect(result.current.currentBackend).toBe('nest');
      expect(result.current.error).toBe(null);
    });

    it('should handle refresh error', async () => {
      // Arrange
      mockConfigService.getConfig.mockResolvedValue(mockConfig);
      const refreshError = new Error('Refresh failed');
      mockConfigService.loadConfig.mockRejectedValue(refreshError);

      // Act - use custom utility to properly handle async hook
      const { result } = await renderAsyncHook(() => useBackendConfig());

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Act
      await act(async () => {
        await result.current.refreshConfig();
      });

      // Assert
      expect(result.current.error).toBe('Refresh failed');
      expect(result.current.loading).toBe(false);
    });

    it('should set loading state during refresh', async () => {
      // Arrange
      mockConfigService.getConfig.mockResolvedValue(mockConfig);
      let resolveRefresh: (value: any) => void;
      const refreshPromise = new Promise(resolve => {
        resolveRefresh = resolve;
      });
      mockConfigService.loadConfig.mockReturnValue(refreshPromise);

      // Act - use custom utility to properly handle async hook
      const { result } = await renderAsyncHook(() => useBackendConfig());

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Act
      act(() => {
        result.current.refreshConfig();
      });

      // Assert - should be loading during refresh
      expect(result.current.loading).toBe(true);

      // Complete the refresh
      act(() => {
        resolveRefresh!(mockConfig);
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });
    });
  });

  describe('Fallback Values', () => {
    it('should provide fallback values when config is null', () => {
      // Arrange
      mockConfigService.getConfig.mockImplementation(() => new Promise(() => {})); // Never resolves

      // Act
      const { result } = renderHook(() => useBackendConfig());

      // Assert
      expect(result.current.currentBackend).toBe('spring');
      expect(result.current.backendUrl).toBe('http://localhost:8080');
    });

    it('should use config values when available', async () => {
      // Arrange
      const customConfig = {
        ...mockConfig,
        currentBackend: 'django' as const,
        backendUrl: 'http://custom:9000',
      };
      mockConfigService.getConfig.mockResolvedValue(customConfig);

      // Act - use custom utility to properly handle async hook
      const { result } = await renderAsyncHook(() => useBackendConfig());

      // Assert - wait for async operations to complete
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.currentBackend).toBe('django');
      expect(result.current.backendUrl).toBe('http://custom:9000');
    });
  });

  describe('Service Integration', () => {


    it('should call configService.loadConfig on refresh', async () => {
      // Arrange
      mockConfigService.getConfig.mockResolvedValue(mockConfig);
      mockConfigService.loadConfig.mockResolvedValue(mockConfig);

      // Act - use custom utility to properly handle async hook
      const { result } = await renderAsyncHook(() => useBackendConfig());

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Act
      await act(async () => {
        await result.current.refreshConfig();
      });

      // Assert
      expect(mockConfigService.loadConfig).toHaveBeenCalledTimes(1);
    });
  });
});
