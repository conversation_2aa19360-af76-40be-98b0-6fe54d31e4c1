import React from 'react';
import { render } from '@testing-library/react';
import PasswordInput from '@/components/common/PasswordInput';

let mockBasePasswordInput;
jest.mock('@/components/common/BasePasswordInput', () => {
  mockBasePasswordInput = jest.fn(() => <div data-testid="base-password-input" />);
  return mockBasePasswordInput;
});
import BasePasswordInput from '@/components/common/BasePasswordInput';

describe('PasswordInput', () => {
  const requiredProps = {
    label: 'Password',
    value: '',
    onChange: jest.fn(),
  };

  beforeEach(() => {
    mockBasePasswordInput.mockClear();
  });

  it('renders BasePasswordInput with default autoComplete', () => {
    render(<PasswordInput {...requiredProps} />);
    expect(mockBasePasswordInput).toHaveBeenCalledWith(
      expect.objectContaining({
        autoComplete: 'current-password',
      }),
      {}
    );
  });

  it('forwards all props to BasePasswordInput', () => {
    const props = {
      ...requiredProps,
      error: true,
      helperText: 'Error!',
      disabled: true,
      showStartAdornment: false,
      placeholder: 'Password',
      size: 'medium',
      sx: { color: 'red' },
    };
    render(<PasswordInput {...props} />);
    expect(mockBasePasswordInput).toHaveBeenCalledWith(
      expect.objectContaining(props),
      {}
    );
  });

  it('overrides default autoComplete if provided', () => {
    render(<PasswordInput {...requiredProps} autoComplete="custom" />);
    expect(mockBasePasswordInput).toHaveBeenCalledWith(
      expect.objectContaining({ autoComplete: 'custom' }),
      {}
    );
  });
}); 