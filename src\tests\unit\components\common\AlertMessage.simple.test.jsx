import React from 'react';
import { render, screen } from '@testing-library/react';
import AlertMessage from '../../../../components/common/AlertMessage';

// Mock MUI components
jest.mock('@mui/material', () => ({
  Alert: ({ children, severity, sx, ...props }) => (
    <div 
      data-testid="alert"
      data-severity={severity}
      {...props}
    >
      {children}
    </div>
  ),
  AlertTitle: ({ children }) => (
    <div data-testid="alert-title">{children}</div>
  ),
}));

describe('AlertMessage - Simple Tests', () => {
  describe('Component Rendering', () => {
    it('renders alert component', () => {
      render(<AlertMessage message="Test message" />);
      
      expect(screen.getByTestId('alert')).toBeInTheDocument();
    });

    it('displays the message text', () => {
      const message = "This is a test message";
      render(<AlertMessage message={message} />);
      
      expect(screen.getByText(message)).toBeInTheDocument();
    });

    it('renders with default severity', () => {
      render(<AlertMessage message="Test message" />);
      
      const alert = screen.getByTestId('alert');
      expect(alert).toHaveAttribute('data-severity', 'error');
    });

    it('renders with custom severity', () => {
      render(<AlertMessage message="Test message" severity="success" />);
      
      const alert = screen.getByTestId('alert');
      expect(alert).toHaveAttribute('data-severity', 'success');
    });

    it('renders with warning severity', () => {
      render(<AlertMessage message="Test message" severity="warning" />);
      
      const alert = screen.getByTestId('alert');
      expect(alert).toHaveAttribute('data-severity', 'warning');
    });

    it('renders with info severity', () => {
      render(<AlertMessage message="Test message" severity="info" />);
      
      const alert = screen.getByTestId('alert');
      expect(alert).toHaveAttribute('data-severity', 'info');
    });
  });

  describe('Message Content', () => {
    it('displays long message', () => {
      const longMessage = "This is a very long message that should still be displayed correctly in the alert component";
      render(<AlertMessage message={longMessage} />);
      
      expect(screen.getByText(longMessage)).toBeInTheDocument();
    });

    it('displays message with special characters', () => {
      const specialMessage = "Message with special chars: !@#$%^&*()";
      render(<AlertMessage message={specialMessage} />);
      
      expect(screen.getByText(specialMessage)).toBeInTheDocument();
    });
  });

  describe('Severity Types', () => {
    it('handles all severity types', () => {
      const severities = ['error', 'warning', 'info', 'success'];
      
      severities.forEach(severity => {
        const { unmount } = render(<AlertMessage message="Test" severity={severity} />);
        
        const alert = screen.getByTestId('alert');
        expect(alert).toHaveAttribute('data-severity', severity);
        
        unmount();
      });
    });

    it('defaults to error severity when not specified', () => {
      render(<AlertMessage message="Test message" />);
      
      const alert = screen.getByTestId('alert');
      expect(alert).toHaveAttribute('data-severity', 'error');
    });
  });

  describe('Component Props', () => {
    it('accepts custom props', () => {
      render(<AlertMessage message="Test" data-custom="value" />);
      
      const alert = screen.getByTestId('alert');
      expect(alert).toHaveAttribute('data-custom', 'value');
    });

    it('handles number message', () => {
      render(<AlertMessage message={123} />);
      
      expect(screen.getByText('123')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('renders without errors', () => {
      expect(() => {
        render(<AlertMessage message="Test message" />);
      }).not.toThrow();
    });

    it('handles multiple renders', () => {
      const { rerender } = render(<AlertMessage message="First message" />);
      
      expect(screen.getByText('First message')).toBeInTheDocument();
      
      rerender(<AlertMessage message="Second message" />);
      
      expect(screen.getByText('Second message')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('renders with proper alert role', () => {
      render(<AlertMessage message="Test message" />);
      
      const alert = screen.getByTestId('alert');
      expect(alert).toBeInTheDocument();
    });

    it('is accessible to screen readers', () => {
      render(<AlertMessage message="Important message" />);
      
      expect(screen.getByText('Important message')).toBeInTheDocument();
    });
  });

  describe('Common Use Cases', () => {
    it('displays error message', () => {
      render(<AlertMessage message="An error occurred" severity="error" />);
      
      expect(screen.getByText('An error occurred')).toBeInTheDocument();
      expect(screen.getByTestId('alert')).toHaveAttribute('data-severity', 'error');
    });

    it('displays success message', () => {
      render(<AlertMessage message="Operation successful" severity="success" />);
      
      expect(screen.getByText('Operation successful')).toBeInTheDocument();
      expect(screen.getByTestId('alert')).toHaveAttribute('data-severity', 'success');
    });

    it('displays warning message', () => {
      render(<AlertMessage message="Please check your input" severity="warning" />);
      
      expect(screen.getByText('Please check your input')).toBeInTheDocument();
      expect(screen.getByTestId('alert')).toHaveAttribute('data-severity', 'warning');
    });

    it('displays info message', () => {
      render(<AlertMessage message="For your information" severity="info" />);
      
      expect(screen.getByText('For your information')).toBeInTheDocument();
      expect(screen.getByTestId('alert')).toHaveAttribute('data-severity', 'info');
    });
  });
});
