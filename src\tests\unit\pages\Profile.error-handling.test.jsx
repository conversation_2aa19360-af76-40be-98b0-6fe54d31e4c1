import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import Profile from '../../../pages/Profile';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock sonner toast
const mockToast = {
  success: jest.fn(),
  error: jest.fn(),
};

jest.mock('sonner', () => ({
  toast: mockToast,
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, sx, component, ...props }) => <div data-testid="box">{children}</div>,
  Container: ({ children, maxWidth, sx }) => <div data-testid="container">{children}</div>,
  Paper: ({ children, sx }) => <div data-testid="paper">{children}</div>,
  Typography: ({ children, variant, sx, color }) => (
    <div data-testid={`typography-${variant}`}>{children}</div>
  ),
  TextField: ({
    label,
    value,
    onChange,
    disabled,
    fullWidth,
    InputProps,
    size,
    error,
    helperText,
    ...props
  }) => (
    <div>
      <label>{label}</label>
      <input
        data-testid={`input-${label.toLowerCase().replace(/\s+/g, '-')}`}
        value={value}
        onChange={onChange}
        disabled={disabled}
      />
      {error && <div data-testid="error-text">{helperText}</div>}
    </div>
  ),
  Button: ({ children, onClick, variant, disabled, startIcon, sx, ...props }) => (
    <button
      data-testid={`button-${children.toLowerCase().replace(/\s+/g, '-')}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  ),
  Avatar: ({ src, sx, children }) => (
    <div data-testid="avatar">{children || 'Avatar'}</div>
  ),
  IconButton: ({ children, onClick, sx }) => (
    <button data-testid="icon-button" onClick={onClick}>
      {children}
    </button>
  ),
  Grid: ({ children, container, item, xs, sm, md, lg, xl, spacing, sx }) => (
    <div data-testid="grid">{children}</div>
  ),
  Card: ({ children, sx }) => <div data-testid="card">{children}</div>,
  CardContent: ({ children }) => <div data-testid="card-content">{children}</div>,
  Divider: ({ sx }) => <hr data-testid="divider" />,
  Chip: ({ label, color, variant, sx }) => (
    <span data-testid="chip">{label}</span>
  ),
}));

// Mock MUI icons - using React.createElement to avoid JSX issues
jest.mock('@mui/icons-material/Edit', () => {
  const React = require('react');
  return function Edit() {
    return React.createElement('span', { 'data-testid': 'edit-icon' }, 'Edit');
  };
});

jest.mock('@mui/icons-material/Person', () => {
  const React = require('react');
  return function Person() {
    return React.createElement('span', { 'data-testid': 'person-icon' }, 'Person');
  };
});

jest.mock('@mui/icons-material/Email', () => {
  const React = require('react');
  return function Email() {
    return React.createElement('span', { 'data-testid': 'email-icon' }, 'Email');
  };
});

jest.mock('@mui/icons-material/Phone', () => {
  const React = require('react');
  return function Phone() {
    return React.createElement('span', { 'data-testid': 'phone-icon' }, 'Phone');
  };
});

jest.mock('@mui/icons-material/CalendarToday', () => {
  const React = require('react');
  return function CalendarToday() {
    return React.createElement('span', { 'data-testid': 'calendar-icon' }, 'Calendar');
  };
});

jest.mock('@mui/icons-material/PhotoCamera', () => {
  const React = require('react');
  return function PhotoCamera() {
    return React.createElement('span', { 'data-testid': 'camera-icon' }, 'Camera');
  };
});

const ProfileWrapper = () => (
  <BrowserRouter>
    <Profile />
  </BrowserRouter>
);

describe('Profile - Error Handling Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Save Operation Error Handling', () => {
    it('handles save operation successfully', async () => {
      render(<ProfileWrapper />);

      const editButton = screen.getByTestId('button-edit-profile');
      fireEvent.click(editButton);

      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'Updated Name' } });

      const saveButton = screen.getByTestId('button-save');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalledWith('Profile updated successfully');
      }, { timeout: 2000 });

      expect(screen.getByTestId('button-edit-profile')).toBeInTheDocument();
    });

    it('handles save operation with error', async () => {
      // Mock setTimeout to reject immediately for testing
      const originalSetTimeout = global.setTimeout;
      global.setTimeout = jest.fn((callback, delay) => {
        if (delay === 1000) {
          // This is our simulated API call - make it reject
          callback();
          throw new Error('Simulated API Error');
        } else {
          return originalSetTimeout(callback, delay);
        }
      });

      render(<ProfileWrapper />);

      const editButton = screen.getByTestId('button-edit-profile');
      fireEvent.click(editButton);

      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'Updated Name' } });

      const saveButton = screen.getByTestId('button-save');
      
      // Use act to handle the async operation
      await act(async () => {
        fireEvent.click(saveButton);
        
        // Wait for the error to be thrown
        try {
          await new Promise((resolve, reject) => {
            setTimeout(() => reject(new Error('Simulated API Error')), 1000);
          });
        } catch (error) {
          // Expected error
        }
      });

      await waitFor(() => {
        expect(mockToast.error).toHaveBeenCalledWith('Failed to update profile');
      }, { timeout: 2000 });

      // Restore original setTimeout
      global.setTimeout = originalSetTimeout;
    });

    it('sets loading state during save operation', async () => {
      render(<ProfileWrapper />);

      const editButton = screen.getByTestId('button-edit-profile');
      fireEvent.click(editButton);

      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'Updated Name' } });

      const saveButton = screen.getByTestId('button-save');
      
      // Click save and verify loading behavior
      act(() => {
        fireEvent.click(saveButton);
      });

      // Wait for the operation to complete
      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalled();
      }, { timeout: 2000 });
    });

    it('updates user data after successful save', async () => {
      render(<ProfileWrapper />);

      const editButton = screen.getByTestId('button-edit-profile');
      fireEvent.click(editButton);

      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'New Name' } });

      const saveButton = screen.getByTestId('button-save');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalledWith('Profile updated successfully');
      }, { timeout: 2000 });

      // Verify the form is no longer in edit mode
      expect(screen.getByTestId('button-edit-profile')).toBeInTheDocument();
      expect(screen.queryByTestId('button-save')).not.toBeInTheDocument();
    });

    it('exits edit mode after successful save', async () => {
      render(<ProfileWrapper />);

      const editButton = screen.getByTestId('button-edit-profile');
      fireEvent.click(editButton);

      // Verify we're in edit mode
      expect(screen.getByTestId('button-save')).toBeInTheDocument();
      expect(screen.getByTestId('button-cancel')).toBeInTheDocument();

      const saveButton = screen.getByTestId('button-save');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalled();
      }, { timeout: 2000 });

      // Verify we're no longer in edit mode
      expect(screen.getByTestId('button-edit-profile')).toBeInTheDocument();
      expect(screen.queryByTestId('button-save')).not.toBeInTheDocument();
      expect(screen.queryByTestId('button-cancel')).not.toBeInTheDocument();
    });

    it('handles cancel operation correctly', () => {
      render(<ProfileWrapper />);

      const editButton = screen.getByTestId('button-edit-profile');
      fireEvent.click(editButton);

      const nameInput = screen.getByTestId('input-full-name');
      fireEvent.change(nameInput, { target: { value: 'Changed Name' } });

      const cancelButton = screen.getByTestId('button-cancel');
      fireEvent.click(cancelButton);

      // Verify we're no longer in edit mode
      expect(screen.getByTestId('button-edit-profile')).toBeInTheDocument();
      expect(screen.queryByTestId('button-save')).not.toBeInTheDocument();
      expect(screen.queryByTestId('button-cancel')).not.toBeInTheDocument();
    });
  });

  describe('Loading State Management', () => {
    it('manages loading state properly during save', async () => {
      render(<ProfileWrapper />);

      const editButton = screen.getByTestId('button-edit-profile');
      fireEvent.click(editButton);

      const saveButton = screen.getByTestId('button-save');
      
      // The loading state is internal to the component
      // We can verify the operation completes successfully
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalled();
      }, { timeout: 2000 });
    });
  });
});
