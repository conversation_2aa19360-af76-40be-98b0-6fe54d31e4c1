# 🔄 Backend Switching - GitOps ArgoCD Apps Changes

## Overview
This document outlines the changes required in the `gitops-argocd-apps` repository to support dynamic backend URL switching using Kubernetes ConfigMaps, integrated with the current environment structure.

## 🎯 Current GitOps Setup Analysis

Based on the current environment structure:
- **Environment Files**: `.env.dev.*`, `.env.staging.*`, `.env.prod.*`
- **Current Backend**: Spring Boot (*************:8080)
- **Service Names**: `ai-spring-backend-service`, `ai-django-backend-service`, `ai-nest-backend-service`
- **Namespaces**: `ai-spring-backend-dev`, `ai-django-backend-dev`, `ai-nest-backend-dev`

---

## 📁 Files to Create/Update

### 1. `ai-react-frontend/k8s/runtime-config-configmap.yaml`
**Purpose**: ConfigMap for dynamic backend configuration

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-runtime-config
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: runtime-config
    managed-by: argocd
    version: v1.0.0
  annotations:
    argocd.argoproj.io/sync-wave: "1"
    description: "Runtime backend configuration for dynamic switching"
data:
  runtime-config.json: |
    {
      "currentBackend": "spring",
      "backendUrl": "http://*************:8080",
      "environment": "dev",
      "serviceName": "ai-spring-backend-service",
      "namespace": "ai-spring-backend-dev",
      "apiVersion": "v1",
      "lastUpdated": "2024-01-01T00:00:00Z",
      "supportedBackends": [
        {
          "name": "spring",
          "url": "http://*************:8080",
          "internalUrl": "http://ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local:8080",
          "serviceName": "ai-spring-backend-service",
          "namespace": "ai-spring-backend-dev",
          "status": "ready",
          "healthEndpoint": "/actuator/health"
        },
        {
          "name": "django",
          "url": "http://*************:8000",
          "internalUrl": "http://ai-django-backend-service.ai-django-backend-dev.svc.cluster.local:8000",
          "serviceName": "ai-django-backend-service",
          "namespace": "ai-django-backend-dev",
          "status": "ready",
          "healthEndpoint": "/health"
        },
        {
          "name": "nest",
          "url": "http://**************:3000",
          "internalUrl": "http://ai-nest-backend-service.ai-nest-backend-dev.svc.cluster.local:3000",
          "serviceName": "ai-nest-backend-service",
          "namespace": "ai-nest-backend-dev",
          "status": "pending",
          "healthEndpoint": "/health"
        }
      ]
    }
```

### 2. `ai-react-frontend/k8s/deployment.yaml` (Update)
**Purpose**: Mount ConfigMap and add environment variables

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend
  namespace: ai-react-frontend-dev
spec:
  template:
    spec:
      containers:
      - name: ai-react-frontend
        image: your-registry/ai-react-frontend:latest
        env:
        - name: VITE_APP_ENV
          value: "dev"
        - name: VITE_APP_SERVICE_NAME
          value: "ai-spring-backend-service"
        - name: VITE_APP_BACKEND_NAMESPACE
          value: "ai-spring-backend-dev"
        - name: VITE_USE_RUNTIME_CONFIG
          value: "true"
        volumeMounts:
        - name: runtime-config
          mountPath: /etc/nginx/config
          readOnly: true
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
          readOnly: true
      volumes:
      - name: runtime-config
        configMap:
          name: ai-react-frontend-runtime-config
      - name: nginx-config
        configMap:
          name: ai-react-frontend-nginx-config
```

### 3. `ai-react-frontend/k8s/nginx-configmap.yaml` (Update)
**Purpose**: Add /api/config endpoint for runtime configuration

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-nginx-config
  namespace: ai-react-frontend-dev
data:
  default.conf: |
    server {
        listen 3000;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Runtime configuration endpoint
        location /api/config {
            alias /etc/nginx/config/runtime-config.json;
            add_header Content-Type application/json;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type";
        }

        # Handle preflight requests
        location /api/config {
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, OPTIONS";
                add_header Access-Control-Allow-Headers "Content-Type";
                add_header Content-Length 0;
                return 204;
            }
        }

        # React app
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Static assets with caching
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
```

### 4. `ai-react-frontend/k8s/backend-switch-scripts.yaml` (Optional)
**Purpose**: ConfigMap with helper scripts for backend switching

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-switch-scripts
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: switch-scripts
data:
  switch-to-spring.sh: |
    #!/bin/bash
    set -e
    echo "🔄 Switching to Spring Boot backend..."
    
    kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
      "data": {
        "runtime-config.json": "{\"currentBackend\":\"spring\",\"backendUrl\":\"http://*************:8080\",\"environment\":\"dev\",\"serviceName\":\"ai-spring-backend-service\",\"namespace\":\"ai-spring-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
      }
    }'
    
    kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
    kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
    echo "✅ Switched to Spring Boot backend (*************:8080)"

  switch-to-django.sh: |
    #!/bin/bash
    set -e
    echo "🔄 Switching to Django backend..."
    
    kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
      "data": {
        "runtime-config.json": "{\"currentBackend\":\"django\",\"backendUrl\":\"http://*************:8000\",\"environment\":\"dev\",\"serviceName\":\"ai-django-backend-service\",\"namespace\":\"ai-django-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
      }
    }'
    
    kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
    kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
    echo "✅ Switched to Django backend (*************:8000)"

  switch-to-nest.sh: |
    #!/bin/bash
    set -e
    echo "🔄 Switching to NestJS backend..."
    
    # Check if NestJS backend is ready
    if ! kubectl get service ai-nest-backend-service -n ai-nest-backend-dev >/dev/null 2>&1; then
      echo "❌ NestJS backend service not found. Please deploy NestJS backend first."
      exit 1
    fi
    
    kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
      "data": {
        "runtime-config.json": "{\"currentBackend\":\"nest\",\"backendUrl\":\"http://**************:3000\",\"environment\":\"dev\",\"serviceName\":\"ai-nest-backend-service\",\"namespace\":\"ai-nest-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
      }
    }'
    
    kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
    kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
    echo "✅ Switched to NestJS backend (**************:3000)"

  get-current-backend.sh: |
    #!/bin/bash
    echo "📋 Current Backend Configuration:"
    kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq .
    
    echo -e "\n🔍 Backend Health Status:"
    CURRENT_URL=$(kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq -r .backendUrl)
    BACKEND_TYPE=$(kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq -r .currentBackend)
    
    case $BACKEND_TYPE in
      "spring")
        curl -f $CURRENT_URL/actuator/health && echo "✅ Spring Boot is healthy" || echo "❌ Spring Boot is not responding"
        ;;
      "django")
        curl -f $CURRENT_URL/health && echo "✅ Django is healthy" || echo "❌ Django is not responding"
        ;;
      "nest")
        curl -f $CURRENT_URL/health && echo "✅ NestJS is healthy" || echo "❌ NestJS is not responding"
        ;;
    esac
```

---

## 🔄 Environment-Specific Configurations

### Development Environment ConfigMap
```yaml
data:
  runtime-config.json: |
    {
      "currentBackend": "spring",
      "backendUrl": "http://*************:8080",
      "environment": "dev",
      "serviceName": "ai-spring-backend-service",
      "namespace": "ai-spring-backend-dev"
    }
```

### Staging Environment ConfigMap
```yaml
data:
  runtime-config.json: |
    {
      "currentBackend": "spring",
      "backendUrl": "http://ai-spring-backend-service.ai-spring-backend-staging.svc.cluster.local:8080",
      "environment": "staging",
      "serviceName": "ai-spring-backend-service",
      "namespace": "ai-spring-backend-staging"
    }
```

### Production Environment ConfigMap
```yaml
data:
  runtime-config.json: |
    {
      "currentBackend": "spring",
      "backendUrl": "http://ai-spring-backend-service.ai-spring-backend-prod.svc.cluster.local:8080",
      "environment": "prod",
      "serviceName": "ai-spring-backend-service",
      "namespace": "ai-spring-backend-prod"
    }
```

---

## 🚀 GitOps Workflow

### 1. Commit Changes to GitOps Repository
```bash
cd gitops-argocd-apps
git checkout -b feature/backend-switching-runtime-config

# Add new files
git add ai-react-frontend/k8s/runtime-config-configmap.yaml
git add ai-react-frontend/k8s/backend-switch-scripts.yaml

# Update existing files
git add ai-react-frontend/k8s/deployment.yaml
git add ai-react-frontend/k8s/nginx-configmap.yaml

git commit -m "feat: Add runtime backend switching support

- Add runtime-config-configmap.yaml for dynamic backend switching
- Add backend-switch-scripts.yaml for operational convenience  
- Update deployment.yaml with ConfigMap volume mount
- Update nginx-configmap.yaml with /api/config endpoint
- Support current environment structure (.env.dev.*, .env.staging.*, .env.prod.*)
- Enable runtime backend switching without image rebuilds

Current backend URLs:
- Spring: http://*************:8080 (ai-spring-backend-service)
- Django: http://*************:8000 (ai-django-backend-service)
- NestJS: http://**************:3000 (ai-nest-backend-service)

Closes: #ISSUE_NUMBER"

git push origin feature/backend-switching-runtime-config
```

### 2. ArgoCD Sync and Verification
```bash
# Check ArgoCD application status
argocd app get ai-react-frontend

# Sync the application
argocd app sync ai-react-frontend

# Wait for sync to complete
argocd app wait ai-react-frontend --timeout 300

# Verify ConfigMap created
kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev
```

---

## 🧪 Testing Backend Switching

### 1. Test Current Spring Boot Backend
```bash
# Check current configuration
kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq .

# Test frontend access
curl http://*************:3000/api/config

# Test backend health
curl http://*************:8080/actuator/health
```

### 2. Switch to Django Backend
```bash
# Execute switch script
kubectl exec -it deployment/ai-react-frontend -n ai-react-frontend-dev -- /bin/bash -c "$(kubectl get configmap ai-react-frontend-switch-scripts -n ai-react-frontend-dev -o jsonpath='{.data.switch-to-django\.sh}')"

# Or manual patch
kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "runtime-config.json": "{\"currentBackend\":\"django\",\"backendUrl\":\"http://*************:8000\",\"environment\":\"dev\",\"serviceName\":\"ai-django-backend-service\",\"namespace\":\"ai-django-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
  }
}'

# Restart frontend
kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
```

### 3. Verify Switch
```bash
# Check new configuration
curl http://*************:3000/api/config | jq .currentBackend

# Test Django backend
curl http://*************:8000/health

# Check frontend logs
kubectl logs deployment/ai-react-frontend -n ai-react-frontend-dev | tail -20
```

---

## 📋 Environment Variables Mapping

### Current Environment Structure
```bash
# Development (.env.dev.spring)
VITE_APP_ENV=dev
VITE_APP_API_URL=http://*************:8080
VITE_APP_SERVICE_NAME=ai-spring-backend-service
VITE_APP_BACKEND_NAMESPACE=ai-spring-backend-dev

# Staging (.env.staging.spring)
VITE_APP_ENV=staging
VITE_APP_API_URL=http://ai-spring-backend-service.ai-spring-backend-staging.svc.cluster.local:8080
VITE_APP_SERVICE_NAME=ai-spring-backend-service
VITE_APP_BACKEND_NAMESPACE=ai-spring-backend-staging

# Production (.env.prod.spring)
VITE_APP_ENV=prod
VITE_APP_API_URL=http://ai-spring-backend-service.ai-spring-backend-prod.svc.cluster.local:8080
VITE_APP_SERVICE_NAME=ai-spring-backend-service
VITE_APP_BACKEND_NAMESPACE=ai-spring-backend-prod
```

### Kubernetes Deployment Environment Variables
```yaml
env:
- name: VITE_APP_ENV
  value: "dev"  # or "staging", "prod"
- name: VITE_APP_SERVICE_NAME
  value: "ai-spring-backend-service"
- name: VITE_APP_BACKEND_NAMESPACE
  value: "ai-spring-backend-dev"
- name: VITE_USE_RUNTIME_CONFIG
  value: "true"
```

---

## ✅ Benefits

1. **Environment Alignment**: Matches current `.env.dev.*`, `.env.staging.*`, `.env.prod.*` structure
2. **Current Backend Support**: Works with existing Spring Boot (*************:8080)
3. **Service Name Integration**: Uses `ai-spring-backend-service` and proper namespaces
4. **No Rebuild Required**: Switch backends without rebuilding Docker image
5. **GitOps Integration**: Fully integrated with ArgoCD workflow
6. **Operational Scripts**: Helper scripts for easy backend switching

---

## 📋 Implementation Checklist

### GitOps Repository Changes
- [ ] Create `ai-react-frontend/k8s/runtime-config-configmap.yaml`
- [ ] Create `ai-react-frontend/k8s/backend-switch-scripts.yaml`
- [ ] Update `ai-react-frontend/k8s/deployment.yaml` with ConfigMap mount
- [ ] Update `ai-react-frontend/k8s/nginx-configmap.yaml` with /api/config endpoint
- [ ] Commit and push changes to GitOps repository

### ArgoCD Deployment
- [ ] Sync ArgoCD application
- [ ] Verify ConfigMap created successfully
- [ ] Test /api/config endpoint accessibility
- [ ] Verify frontend deployment updated with new volumes

### Backend Switching Tests
- [ ] Test current Spring Boot backend (*************:8080)
- [ ] Test switch to Django backend (*************:8000)
- [ ] Test switch back to Spring Boot
- [ ] Verify NestJS backend readiness (**************:3000)

### Environment Validation
- [ ] Verify dev environment configuration
- [ ] Test staging environment setup (when available)
- [ ] Test production environment setup (when available)
- [ ] Validate service names and namespaces match current setup

