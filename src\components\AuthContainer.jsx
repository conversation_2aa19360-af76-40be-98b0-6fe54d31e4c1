import React from 'react';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Container, Paper } from '@mui/material';
import PropTypes from 'prop-types';
import { useAuth } from '../contexts/AuthContext';
import LoginForm from './LoginForm';
import SignUpForm from './SignUpForm';
import ForgotPasswordForm from './ForgotPasswordForm';
import UserDashboard from './UserDashboard';

const AuthContainer = ({ initialView = 'login' }) => {
  const [currentView, setCurrentView] = useState(initialView);
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    setCurrentView(initialView);
  }, [initialView]);

  if (user) {
    return <UserDashboard />;
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'login':
        return (
          <LoginForm
            onSwitchToSignUp={() => navigate('/signup')}
            onSwitchToForgotPassword={() => navigate('/forgot-password')}
          />
        );
      case 'signup':
        return (
          <SignUpForm onSwitchToLogin={() => navigate('/signin')} />
        );
      case 'forgot-password':
        return (
          <ForgotPasswordForm onSwitchToLogin={() => navigate('/signin')} />
        );

      default:
        return null;
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: { xs: 2, sm: 3 },
        px: { xs: 1, sm: 2 },
      }}
    >
      <Container maxWidth="sm">
        <Paper
          elevation={24}
          sx={{
            p: { xs: 3, sm: 4 },
            borderRadius: 2,
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            width: '100%',
            maxWidth: 480,
            mx: 'auto',
          }}
        >
          {renderCurrentView()}
        </Paper>
      </Container>
    </Box>
  );
};

AuthContainer.propTypes = {
  initialView: PropTypes.oneOf(['login', 'signup', 'forgot-password']),
};

export default AuthContainer;
