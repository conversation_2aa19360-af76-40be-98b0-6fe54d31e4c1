// Environment configuration helper
import { getApiBaseUrl as getRuntimeApiBaseUrl } from '../services/configService';

// Synchronous API base URL getter with runtime config support
export const getApiBaseUrl = (): string => {
  // For testing environment
  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
    return process.env.VITE_APP_API_URL || 'http://localhost:8080';
  }

  // For runtime environment - check window._env_ first
  if (typeof window !== 'undefined' && window._env_?.REACT_APP_BACKEND_URL) {
    return window._env_.REACT_APP_BACKEND_URL;
  }

  // Fallback to build-time environment
  try {
    return import.meta.env.VITE_APP_API_URL || 'http://localhost:8080';
  } catch {
    return 'http://localhost:8080';
  }
};

// New async API base URL getter (recommended for new code)
export const getApiBaseUrlAsync = async (): Promise<string> => {
  try {
    return await getRuntimeApiBaseUrl();
  } catch {
    return getApiBaseUrl();
  }
};

// Runtime environment detection
export const getCurrentEnvironment = () => {
  // Check runtime config first
  if (typeof window !== 'undefined' && window._env_?.REACT_APP_ENVIRONMENT) {
    return window._env_.REACT_APP_ENVIRONMENT;
  }

  // Fallback to build-time environment
  return import.meta.env.VITE_APP_ENV || 'dev';
};

// Runtime backend detection
export const getCurrentBackend = () => {
  // Check runtime config first
  if (typeof window !== 'undefined' && window._env_?.REACT_APP_CURRENT_BACKEND) {
    return window._env_.REACT_APP_CURRENT_BACKEND;
  }

  // Fallback to service name detection
  const serviceName = import.meta.env.VITE_APP_SERVICE_NAME || '';
  if (serviceName.includes('spring')) return 'spring';
  if (serviceName.includes('nest')) return 'nest';
  if (serviceName.includes('django')) return 'django';
  return 'spring';
};

// Backend service configuration
export const getServiceConfig = () => {
  return {
    serviceName: import.meta.env.VITE_APP_SERVICE_NAME,
    namespace: import.meta.env.VITE_APP_BACKEND_NAMESPACE,
    port: import.meta.env.VITE_APP_PORT || 3000
  };
};

// API configuration
export const getApiConfig = () => {
  return {
    baseUrl: import.meta.env.VITE_APP_API_URL,
    oauthUrl: import.meta.env.VITE_APP_GOOGLE_OAUTH_URL
  };
};

// Runtime configuration utilities
export const isRuntimeConfigAvailable = (): boolean => {
  return typeof window !== 'undefined' && !!window._env_;
};

export const getRuntimeConfig = () => {
  if (isRuntimeConfigAvailable()) {
    return window._env_;
  }
  return null;
};

export const isUsingRuntimeConfig = (): boolean => {
  return isRuntimeConfigAvailable() && window._env_.REACT_APP_USE_RUNTIME_CONFIG === 'true';
};

// Environment checks
export const isDevEnvironment = getCurrentEnvironment() === 'dev' || getCurrentEnvironment() === 'development';
export const isStagingEnvironment = getCurrentEnvironment() === 'staging';
export const isProdEnvironment = getCurrentEnvironment() === 'prod';
export const isKubernetesEnvironment = isDevEnvironment || isStagingEnvironment || isProdEnvironment;
