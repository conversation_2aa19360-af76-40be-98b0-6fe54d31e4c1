import { renderHook, act } from '@testing-library/react';
import useApiCall from '@/hooks/useApiCall';
import { toast } from 'sonner';

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

describe('useApiCall', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('handles successful API call with success message and callback', async () => {
    const mockApiCall = jest.fn().mockResolvedValue('success result');
    const onSuccess = jest.fn();
    const { result } = renderHook(() => useApiCall());

    await act(async () => {
      await result.current.execute(mockApiCall, {
        onSuccess,
        successMessage: 'Success!',
      });
    });

    expect(mockApiCall).toHaveBeenCalled();
    expect(result.current.success).toBe(true);
    expect(result.current.error).toBe('');
    expect(result.current.loading).toBe(false);
    expect(onSuccess).toHaveBeenCalledWith('success result');
    expect(toast.success).toHaveBeenCalledWith('Success!');
  });

  it('handles API error with error message, toast, and callback', async () => {
    const mockApiCall = jest.fn().mockRejectedValue(new Error('API Error'));
    const onError = jest.fn();
    const { result } = renderHook(() => useApiCall());

    await act(async () => {
      await result.current.execute(mockApiCall, {
        onError,
        errorMessage: 'Custom error',
      });
    });

    expect(mockApiCall).toHaveBeenCalled();
    expect(result.current.success).toBe(false);
    expect(result.current.error).toBe('Custom error');
    expect(result.current.loading).toBe(false);
    expect(onError).toHaveBeenCalledWith(expect.any(Error));
    expect(toast.error).toHaveBeenCalledWith('Custom error');
  });

  it('prevents multiple simultaneous calls', async () => {
    const mockApiCall = jest.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve('result'), 100))
    );
    const { result } = renderHook(() => useApiCall());

    // Start first call
    const promise1 = act(async () => {
      return result.current.execute(mockApiCall);
    });

    // Try to start second call immediately
    const promise2 = act(async () => {
      return result.current.execute(mockApiCall);
    });

    await Promise.all([promise1, promise2]);

    // Should only call the API once
    expect(mockApiCall).toHaveBeenCalledTimes(1);
  });

  it('resets state with reset', () => {
    const { result } = renderHook(() => useApiCall());

    act(() => {
      result.current.setError('test error');
      result.current.setSuccess(true);
    });

    expect(result.current.error).toBe('test error');
    expect(result.current.success).toBe(true);

    act(() => {
      result.current.reset();
    });

    expect(result.current.error).toBe('');
    expect(result.current.success).toBe(false);
    expect(result.current.loading).toBe(false);
  });

  it('does not show toast if showToast is false', async () => {
    const mockApiCall = jest.fn().mockRejectedValue(new Error('API Error'));
    const { result } = renderHook(() => useApiCall());

    await act(async () => {
      await result.current.execute(mockApiCall, {
        showToast: false,
      });
    });

    expect(toast.error).not.toHaveBeenCalled();
    expect(result.current.error).toBe('API Error');
  });

  it('does not reset state if resetOnStart is false', async () => {
    const mockApiCall = jest.fn().mockResolvedValue('result');
    const { result } = renderHook(() => useApiCall());

    // Set initial state
    act(() => {
      result.current.setError('existing error');
      result.current.setSuccess(true);
    });

    await act(async () => {
      await result.current.execute(mockApiCall, {
        resetOnStart: false,
      });
    });

    // Should not reset existing state
    expect(result.current.error).toBe('existing error');
    expect(result.current.success).toBe(true);
  });
});
