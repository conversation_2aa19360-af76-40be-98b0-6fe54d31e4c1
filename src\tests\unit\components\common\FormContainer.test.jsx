import React from 'react';
import { render, screen } from '@testing-library/react';
import FormContainer from '@/components/common/FormContainer';

jest.mock('@mui/material', () => ({
  Box: jest.fn(({ children, ...props }) => <div data-testid="mui-box" {...props}>{children}</div>),
  Paper: jest.fn(({ children, ...props }) => <div data-testid="mui-paper" {...props}>{children}</div>),
}));

describe('FormContainer', () => {
  it('renders children inside Paper and Box', () => {
    render(<FormContainer><span data-testid="child">Child</span></FormContainer>);
    expect(screen.getByTestId('mui-box')).toBeInTheDocument();
    expect(screen.getByTestId('mui-paper')).toContainElement(screen.getByTestId('child'));
  });

  it('renders logo when showLogo is true', () => {
    render(<FormContainer showLogo={true}>X</FormContainer>);
    expect(screen.getByAltText('Brand Logo')).toBeInTheDocument();
  });

  it('does not render logo when showLogo is false', () => {
    render(<FormContainer showLogo={false}>X</FormContainer>);
    expect(screen.queryByAltText('Brand Logo')).toBeNull();
  });

  it('applies custom maxWidth and sx', () => {
    render(<FormContainer maxWidth={600} sx={{ color: 'red' }}>X</FormContainer>);
    const { Paper } = require('@mui/material');
    const lastCall = Paper.mock.calls[Paper.mock.calls.length - 1][0];
    // sx is merged, so color: red should be present in Box
    expect(screen.getByTestId('mui-box')).toHaveAttribute('sx');
    expect(lastCall.sx).toBeDefined();
  });

  it('forwards extra props to Box', () => {
    render(<FormContainer data-custom="foo">X</FormContainer>);
    expect(screen.getByTestId('mui-box')).toHaveAttribute('data-custom', 'foo');
  });
}); 