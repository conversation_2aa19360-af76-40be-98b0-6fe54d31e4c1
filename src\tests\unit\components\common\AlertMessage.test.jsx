import React from 'react';
import { render, screen } from '@testing-library/react';
import AlertMessage from '@/components/common/AlertMessage';

jest.mock('@mui/material', () => ({
  Alert: jest.fn(({ children, ...props }) => <div data-testid="mui-alert" {...props}>{children}</div>),
}));

describe('AlertMessage', () => {
  it('renders nothing if show is false', () => {
    const { container } = render(<AlertMessage message="Test" show={false} />);
    expect(container.firstChild).toBeNull();
  });

  it('renders nothing if message is empty', () => {
    const { container } = render(<AlertMessage message="" show={true} />);
    expect(container.firstChild).toBeNull();
  });

  it('renders with default severity and message', () => {
    render(<AlertMessage message="Error occurred" />);
    const alert = screen.getByTestId('mui-alert');
    expect(alert).toHaveTextContent('Error occurred');
    expect(alert).toHaveAttribute('severity', 'error');
  });

  it('renders with custom severity', () => {
    render(<AlertMessage message="Warning!" severity="warning" />);
    const alert = screen.getByTestId('mui-alert');
    expect(alert).toHaveAttribute('severity', 'warning');
  });

  it('applies custom sx prop', () => {
    render(<AlertMessage message="Styled" sx={{ color: 'red' }} />);
    const alert = screen.getByTestId('mui-alert');
    expect(alert.props?.sx || alert.getAttribute('sx')).toBeDefined();
  });

  it('forwards extra props to Alert', () => {
    render(<AlertMessage message="Extra" data-custom="foo" />);
    const alert = screen.getByTestId('mui-alert');
    expect(alert).toHaveAttribute('data-custom', 'foo');
  });
}); 