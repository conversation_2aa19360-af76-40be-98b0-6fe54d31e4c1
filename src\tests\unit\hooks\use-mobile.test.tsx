import { renderHook, act } from '@testing-library/react';
import { useIsMobile } from '@/hooks/use-mobile';

// Mock window.matchMedia
const mockMatchMedia = jest.fn();
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: mockMatchMedia,
});

// Mock window.innerWidth
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  value: 1024,
});

describe('useIsMobile', () => {
  let mockMediaQueryList: any;

  beforeEach(() => {
    mockMediaQueryList = {
      matches: false,
      media: '(max-width: 767px)',
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };
    mockMatchMedia.mockReturnValue(mockMediaQueryList);
    
    // Reset window.innerWidth to desktop size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      value: 1024,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should return false for desktop width initially', () => {
      // Arrange
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 1024,
      });

      // Act
      const { result } = renderHook(() => useIsMobile());

      // Assert
      expect(result.current).toBe(false);
    });

    it('should return true for mobile width initially', () => {
      // Arrange
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 500,
      });

      // Act
      const { result } = renderHook(() => useIsMobile());

      // Assert
      expect(result.current).toBe(true);
    });

    it('should return true for width exactly at mobile breakpoint', () => {
      // Arrange
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 767,
      });

      // Act
      const { result } = renderHook(() => useIsMobile());

      // Assert
      expect(result.current).toBe(true);
    });

    it('should return false for width exactly at desktop breakpoint', () => {
      // Arrange
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 768,
      });

      // Act
      const { result } = renderHook(() => useIsMobile());

      // Assert
      expect(result.current).toBe(false);
    });
  });

  describe('Media Query Setup', () => {
    it('should create media query with correct breakpoint', () => {
      // Act
      renderHook(() => useIsMobile());

      // Assert
      expect(mockMatchMedia).toHaveBeenCalledWith('(max-width: 767px)');
    });

    it('should add event listener for media query changes', () => {
      // Act
      renderHook(() => useIsMobile());

      // Assert
      expect(mockMediaQueryList.addEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
    });

    it('should remove event listener on unmount', () => {
      // Act
      const { unmount } = renderHook(() => useIsMobile());
      unmount();

      // Assert
      expect(mockMediaQueryList.removeEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
    });
  });

  describe('Window Resize Handling', () => {
    it('should update when window width changes from desktop to mobile', () => {
      // Arrange
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 1024,
      });
      const { result } = renderHook(() => useIsMobile());
      expect(result.current).toBe(false);

      // Act - simulate window resize to mobile
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 500,
        });
        // Simulate the media query change event
        const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
        changeHandler();
      });

      // Assert
      expect(result.current).toBe(true);
    });

    it('should update when window width changes from mobile to desktop', () => {
      // Arrange
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 500,
      });
      const { result } = renderHook(() => useIsMobile());
      expect(result.current).toBe(true);

      // Act - simulate window resize to desktop
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 1024,
        });
        // Simulate the media query change event
        const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
        changeHandler();
      });

      // Assert
      expect(result.current).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined initial state gracefully', () => {
      // Act
      const { result } = renderHook(() => useIsMobile());

      // Assert - should return a boolean, not undefined
      expect(typeof result.current).toBe('boolean');
    });

    it('should handle media query list without addEventListener', () => {
      // Arrange
      const mockMediaQueryListWithoutListener = {
        matches: false,
        media: '(max-width: 767px)',
        addEventListener: undefined,
        removeEventListener: undefined,
      };
      mockMatchMedia.mockReturnValue(mockMediaQueryListWithoutListener);

      // Act & Assert - should handle gracefully
      const { result } = renderHook(() => useIsMobile());
      expect(typeof result.current).toBe('boolean');
    });
  });
});
