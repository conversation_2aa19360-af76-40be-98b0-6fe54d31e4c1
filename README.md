# AI React Frontend – Login Module

A modern React.js application featuring a secure login module, environment-based configuration, and modular code structure. This project is ideal for developers seeking a robust authentication foundation and scalable frontend architecture. It supports multiple environments via automated environment file generation.

## Table of Contents
- [AI React Frontend – Login Module](#ai-react-frontend--login-module)
  - [Table of Contents](#table-of-contents)
  - [Installation](#installation)
  - [Environment Setup](#environment-setup)
  - [Usage](#usage)
  - [Project Structure](#project-structure)
  - [Configuration](#configuration)
  - [Contributing](#contributing)
  - [License](#license)
  - [Contact](#contact)
  - [Acknowledgements](#acknowledgements)

## Installation

1. **Clone the repository:**
   ```sh
   git clone https://github.com/ChidhagniConsulting/ai-react-frontend.git
   cd ai-react-frontend
   ```
2. **Install dependencies:**
   ```sh
   npm install
   ```

## Environment Setup

Before running the project, you must generate the required environment files.

1. **Generate environment files:**
   ```sh
   npm run generate:envs
   ```
   This command runs the script at `scripts/generate-env-files.js` to create the necessary `.env` files for different environments.

2. **Did you generate your environment files?**
   - If not, please run the above command before proceeding.
   - If you need to customize environment variables, edit the generated `.env` files in the project root.

For more details, see [docs/ENV_SETUP.md](docs/ENV_SETUP.md).

## Usage

- **Start the development server:**
  
  Choose your backend type and run the corresponding command:
  
  - For **Spring Boot** backend:
    ```sh
    npm run dev:spring
    ```
  - For **Nest.js** backend:
    ```sh
    npm run dev:nest
    ```
  - For **Django** backend:
    ```sh
    npm run dev:django
    ```
  
  > **Note:**
  > Ensure your backend server (Spring, Nest, or Django) is running and accessible at the API URL specified in your environment files before starting the frontend.
- **Run tests:**
  ```sh
  npm run test:all
  ```
- **Build for production or beta:**
  
  Choose your backend type and run the corresponding build command:
  
  - For **Spring Boot** backend:
    - **Beta build:**
      ```sh
      npm run build:spring.beta
      ```
    - **Production build:**
      ```sh
      npm run build:spring.production
      ```
  - For **Nest.js** backend:
    - **Beta build:**
      ```sh
      npm run build:nest.beta
      ```
    - **Production build:**
      ```sh
      npm run build:nest.production
      ```
  - For **Django** backend:
    - **Beta build:**
      ```sh
      npm run build:django.beta
      ```
    - **Production build:**
      ```sh
      npm run build:django.production
      ```

## Project Structure

- `src/` – Main source code (components, pages, hooks, contexts, etc.)
- `docs/` – Documentation files
- `public/` – Static assets (favicon, images, etc.)
- `scripts/` – Utility scripts (e.g., environment file generation)
- `.env*` – Environment files (generated)
- `package.json` – Project metadata and scripts
- `README.md` – Project overview (this file)

## Configuration

- Environment variables are managed via `.env` files.
- To generate them, run:
  ```sh
  npm run generate:envs
  ```
- For details on each variable, see [docs/ENV_SETUP.md](docs/ENV_SETUP.md).

### SonarQube Configuration

For code quality analysis with SonarQube:

1. **Set up SonarQube token:**
   ```sh
   export SONAR_TOKEN=your_sonarqube_token_here
   ```
   Or pass it as a parameter:
   ```sh
   sonar-scanner -Dsonar.token=your_sonarqube_token_here
   ```

2. **Run SonarQube analysis:**
   ```sh
   sonar-scanner
   ```

3. **Using Docker Compose:**
   ```sh
   docker-compose -f docker-compose.sonarqube.yml up -d
   ```

**Security Note:** Never commit SonarQube tokens to version control. Always use environment variables or secure parameter passing.

## Contributing

Contributions are welcome! Please see [docs/CONTRIBUTING.md](docs/CONTRIBUTING.md) for guidelines.

## License

This project is licensed under the MIT License.

## Contact

For questions or support, open an issue or contact : <EMAIL>

## Acknowledgements

- [React](https://reactjs.org/)
- [Vite](https://vitejs.dev/)
