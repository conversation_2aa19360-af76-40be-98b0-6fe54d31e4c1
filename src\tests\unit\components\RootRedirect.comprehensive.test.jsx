import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import RootRedirect from '../../../components/RootRedirect';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock AuthContext
const mockUseAuth = {
  user: null,
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth,
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, sx }) => <div data-testid="loading-box">{children}</div>,
  CircularProgress: ({ size, sx }) => (
    <div 
      data-testid="loading-spinner" 
      data-size={size} 
      data-color={sx?.color || 'default'}
    />
  ),
}));

// Wrapper component for router
const RootRedirectWrapper = () => (
  <BrowserRouter>
    <RootRedirect />
  </BrowserRouter>
);

describe('RootRedirect - Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
    mockUseAuth.user = null;
    
    // Mock timers
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe('Rendering', () => {
    it('renders loading spinner and box', () => {
      render(<RootRedirectWrapper />);
      
      expect(screen.getByTestId('loading-box')).toBeInTheDocument();
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('renders loading spinner with correct size', () => {
      render(<RootRedirectWrapper />);
      
      const spinner = screen.getByTestId('loading-spinner');
      expect(spinner).toHaveAttribute('data-size', '40');
    });

    it('renders loading spinner with correct color', () => {
      render(<RootRedirectWrapper />);
      
      const spinner = screen.getByTestId('loading-spinner');
      expect(spinner).toHaveAttribute('data-color', 'white');
    });

    it('renders loading box container', () => {
      render(<RootRedirectWrapper />);
      
      const box = screen.getByTestId('loading-box');
      expect(box).toBeInTheDocument();
      expect(box).toContainElement(screen.getByTestId('loading-spinner'));
    });
  });

  describe('Navigation Logic', () => {
    it('redirects to dashboard when user is authenticated', async () => {
      mockUseAuth.user = {
        id: '123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      render(<RootRedirectWrapper />);
      
      // Fast-forward the timer
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    it('redirects to signin when user is not authenticated', async () => {
      mockUseAuth.user = null;

      render(<RootRedirectWrapper />);
      
      // Fast-forward the timer
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });

    it('redirects to signin when user is undefined', async () => {
      mockUseAuth.user = undefined;

      render(<RootRedirectWrapper />);
      
      // Fast-forward the timer
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });

    it('redirects to signin when user is false', async () => {
      mockUseAuth.user = false;

      render(<RootRedirectWrapper />);
      
      // Fast-forward the timer
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });

    it('redirects to signin when user is empty object', async () => {
      mockUseAuth.user = {};

      render(<RootRedirectWrapper />);
      
      // Fast-forward the timer
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    it('redirects to dashboard when user has minimal valid data', async () => {
      mockUseAuth.user = { id: '456' };

      render(<RootRedirectWrapper />);
      
      // Fast-forward the timer
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });
  });

  describe('Timer Behavior', () => {
    it('waits 100ms before redirecting', async () => {
      mockUseAuth.user = { id: '123' };

      render(<RootRedirectWrapper />);
      
      // Should not navigate immediately
      expect(mockNavigate).not.toHaveBeenCalled();
      
      // Advance timer by 50ms - should still not navigate
      jest.advanceTimersByTime(50);
      expect(mockNavigate).not.toHaveBeenCalled();
      
      // Advance timer by another 50ms (total 100ms) - should navigate
      jest.advanceTimersByTime(50);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    it('clears timeout on component unmount', () => {
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
      
      const { unmount } = render(<RootRedirectWrapper />);
      
      // Unmount before timer completes
      unmount();
      
      expect(clearTimeoutSpy).toHaveBeenCalled();
      
      clearTimeoutSpy.mockRestore();
    });

    it('does not navigate if component unmounts before timer', async () => {
      mockUseAuth.user = { id: '123' };

      const { unmount } = render(<RootRedirectWrapper />);
      
      // Unmount before timer completes
      unmount();
      
      // Advance timer
      jest.advanceTimersByTime(100);
      
      // Should not navigate
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('User State Changes', () => {
    it('responds to user state changes', async () => {
      // Start with no user
      mockUseAuth.user = null;

      const { rerender } = render(<RootRedirectWrapper />);
      
      // Change user state
      mockUseAuth.user = { id: '123', email: '<EMAIL>' };
      rerender(<RootRedirectWrapper />);
      
      // Fast-forward timer
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    it('handles user logout scenario', async () => {
      // Start with authenticated user
      mockUseAuth.user = { id: '123' };

      const { rerender } = render(<RootRedirectWrapper />);
      
      // User logs out
      mockUseAuth.user = null;
      rerender(<RootRedirectWrapper />);
      
      // Fast-forward timer
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });

    it('handles multiple user state changes', async () => {
      // Start with no user
      mockUseAuth.user = null;
      const { rerender } = render(<RootRedirectWrapper />);
      
      // Change to authenticated
      mockUseAuth.user = { id: '123' };
      rerender(<RootRedirectWrapper />);
      
      // Change back to unauthenticated
      mockUseAuth.user = null;
      rerender(<RootRedirectWrapper />);
      
      // Fast-forward timer
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });
  });

  describe('Navigation Options', () => {
    it('uses replace option for dashboard navigation', async () => {
      mockUseAuth.user = { id: '123' };

      render(<RootRedirectWrapper />);
      
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    it('uses replace option for signin navigation', async () => {
      mockUseAuth.user = null;

      render(<RootRedirectWrapper />);
      
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });

    it('calls navigate only once per render cycle', async () => {
      mockUseAuth.user = { id: '123' };

      render(<RootRedirectWrapper />);
      
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledTimes(1);
      });
      
      // Advance timer more - should not call navigate again
      jest.advanceTimersByTime(100);
      expect(mockNavigate).toHaveBeenCalledTimes(1);
    });
  });

  describe('Edge Cases', () => {
    it('handles null user object', async () => {
      mockUseAuth.user = null;

      render(<RootRedirectWrapper />);
      
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });

    it('handles user object with falsy values', async () => {
      mockUseAuth.user = { id: '', email: '', name: '' };

      render(<RootRedirectWrapper />);
      
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    it('handles user object with only some properties', async () => {
      mockUseAuth.user = { email: '<EMAIL>' };

      render(<RootRedirectWrapper />);
      
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    it('handles complex user object', async () => {
      mockUseAuth.user = {
        id: '123',
        email: '<EMAIL>',
        name: 'Test User',
        roles: ['user', 'admin'],
        preferences: { theme: 'dark' },
        lastLogin: new Date().toISOString()
      };

      render(<RootRedirectWrapper />);
      
      jest.advanceTimersByTime(100);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });
  });

  describe('Component Lifecycle', () => {
    it('sets up timer on mount', () => {
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');
      
      render(<RootRedirectWrapper />);
      
      expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 100);
      
      setTimeoutSpy.mockRestore();
    });

    it('cleans up timer on unmount', () => {
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
      
      const { unmount } = render(<RootRedirectWrapper />);
      unmount();
      
      expect(clearTimeoutSpy).toHaveBeenCalled();
      
      clearTimeoutSpy.mockRestore();
    });

    it('recreates timer when dependencies change', () => {
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');
      
      mockUseAuth.user = null;
      const { rerender } = render(<RootRedirectWrapper />);
      
      const initialCallCount = setTimeoutSpy.mock.calls.length;
      
      // Change user state
      mockUseAuth.user = { id: '123' };
      rerender(<RootRedirectWrapper />);
      
      expect(setTimeoutSpy.mock.calls.length).toBeGreaterThan(initialCallCount);
      
      setTimeoutSpy.mockRestore();
    });
  });
});
