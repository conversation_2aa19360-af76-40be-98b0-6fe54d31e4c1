import { renderHook, act } from '@testing-library/react';
import useFormValidation from '../../../hooks/useFormValidation';

describe('useFormValidation - Comprehensive Tests', () => {
  const mockInitialValues = {
    email: '',
    name: '',
    mobile: '',
    agreeToTerms: false
  };

  describe('Email Validation', () => {
    it('validates required email field', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.validateForm(['email']);
      });

      expect(result.current.errors.email).toBe('Email is required');
    });

    it('validates invalid email format', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('email', 'invalid-email');
      });

      act(() => {
        result.current.validateForm(['email']);
      });

      expect(result.current.errors.email).toBe('Please enter a valid email address');
    });

    it('validates valid email format', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
      });

      act(() => {
        result.current.validateForm(['email']);
      });

      expect(result.current.errors.email).toBeUndefined();
    });
  });

  describe('Name Validation', () => {
    it('validates required name field', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.validateForm(['name']);
      });

      expect(result.current.errors.name).toBe('Full name is required');
    });

    it('validates name with only whitespace', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('name', '   ');
      });

      act(() => {
        result.current.validateForm(['name']);
      });

      expect(result.current.errors.name).toBe('Full name is required');
    });

    it('validates name minimum length', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('name', 'A');
      });

      act(() => {
        result.current.validateForm(['name']);
      });

      expect(result.current.errors.name).toBe('Name must be at least 2 characters');
    });

    it('validates valid name', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('name', 'John Doe');
      });

      act(() => {
        result.current.validateForm(['name']);
      });

      expect(result.current.errors.name).toBeUndefined();
    });
  });

  describe('Mobile Validation', () => {
    it('validates required mobile field', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.validateForm(['mobile']);
      });

      expect(result.current.errors.mobile).toBe('Mobile number is required');
    });

    it('validates invalid mobile format', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('mobile', '123');
      });

      act(() => {
        result.current.validateForm(['mobile']);
      });

      expect(result.current.errors.mobile).toBe('Please enter a valid mobile number');
    });

    it('validates valid mobile format', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('mobile', '1234567890');
      });

      act(() => {
        result.current.validateForm(['mobile']);
      });

      expect(result.current.errors.mobile).toBeUndefined();
    });
  });

  describe('Terms Agreement Validation', () => {
    it('validates required terms agreement', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.validateForm(['agreeToTerms']);
      });

      expect(result.current.errors.agreeToTerms).toBe('You must agree to the terms and conditions');
    });

    it('validates accepted terms agreement', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('agreeToTerms', true);
      });

      act(() => {
        result.current.validateForm(['agreeToTerms']);
      });

      expect(result.current.errors.agreeToTerms).toBeUndefined();
    });
  });

  describe('Form Validation', () => {
    it('validates all fields when no specific fields provided', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        const isValid = result.current.validateForm();
        expect(isValid).toBe(false);
      });

      expect(result.current.errors.email).toBeDefined();
      expect(result.current.errors.name).toBeDefined();
      expect(result.current.errors.mobile).toBeDefined();
      expect(result.current.errors.agreeToTerms).toBeDefined();
    });

    it('validates specific fields only', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        const isValid = result.current.validateForm(['email', 'name']);
        expect(isValid).toBe(false);
      });

      expect(result.current.errors.email).toBeDefined();
      expect(result.current.errors.name).toBeDefined();
      expect(result.current.errors.mobile).toBeUndefined();
      expect(result.current.errors.agreeToTerms).toBeUndefined();
    });

    it('returns true when all validated fields are valid', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
        result.current.handleInputChange('name', 'John Doe');
        result.current.handleInputChange('mobile', '1234567890');
        result.current.handleInputChange('agreeToTerms', true);
      });

      act(() => {
        const isValid = result.current.validateForm();
        expect(isValid).toBe(true);
      });

      expect(Object.keys(result.current.errors)).toHaveLength(0);
    });

    it('sets errors for invalid fields', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        result.current.handleInputChange('email', 'invalid-email');
        result.current.handleInputChange('name', 'A');
      });

      act(() => {
        result.current.validateForm(['email', 'name']);
      });

      expect(result.current.errors.email).toBe('Please enter a valid email address');
      expect(result.current.errors.name).toBe('Name must be at least 2 characters');
    });
  });

  describe('Error Clearing', () => {
    it('clears error when field value changes', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      // First, create an error
      act(() => {
        result.current.validateForm(['email']);
      });

      expect(result.current.errors.email).toBeDefined();

      // Then, change the field value to clear the error
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
      });

      expect(result.current.errors.email).toBeUndefined();
    });

    it('does not affect other field errors when clearing one field', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      // Create errors for multiple fields
      act(() => {
        result.current.validateForm(['email', 'name']);
      });

      expect(result.current.errors.email).toBeDefined();
      expect(result.current.errors.name).toBeDefined();

      // Clear error for one field
      act(() => {
        result.current.handleInputChange('email', '<EMAIL>');
      });

      expect(result.current.errors.email).toBeUndefined();
      expect(result.current.errors.name).toBeDefined();
    });
  });

  describe('Unknown Field Validation', () => {
    it('handles validation for unknown fields gracefully', () => {
      const { result } = renderHook(() => 
        useFormValidation(mockInitialValues)
      );

      act(() => {
        const isValid = result.current.validateForm(['unknownField']);
        expect(isValid).toBe(true);
      });

      expect(result.current.errors.unknownField).toBeUndefined();
    });
  });
});
