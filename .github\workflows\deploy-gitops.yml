name: G<PERSON>ps Deployment trigger

on:
  workflow_call:
    secrets:
      GITOPS_TOKEN:
        required: true

jobs:
  gitops-deploy:
    runs-on: [self-hosted, Linux]
    environment: ${{ github.ref_name == 'main' && 'production' || github.ref_name == 'staging' && 'staging' || 'dev' }}
    steps:
      - name: 🚀 Trigger GitOps Deployment
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITOPS_TOKEN }}
          script: |
            console.log('🚀 Triggering GitOps deployment for React application...');
            console.log('Branch:', '${{ github.ref_name }}');
            console.log('Event:', '${{ github.event_name }}');

            // Use latest tag for universal runtime-configured deployment
            const dockerTag = 'latest';

            console.log('Using universal Docker image tag:', dockerTag);

            // Determine environment based on branch
            let environment = 'dev';  // Default for feature branches
            if ('${{ github.ref_name }}' === 'main') {
              environment = 'production';  // Main branch goes to production
            } else if ('${{ github.ref_name }}' === 'staging') {
              environment = 'staging';  // Staging branch goes to staging
            }

            // Docker tag is always 'latest' - no validation needed

            const payload = {
              app_name: 'AI React Frontend',
              project_id: 'ai-react-frontend',
              application_type: 'react-frontend',
              environment: environment,
              docker_image: 'registry.digitalocean.com/doks-registry/ai-react-frontend',
              docker_tag: dockerTag,
              source_repo: `${context.repo.owner}/${context.repo.repo}`,
              source_branch: '${{ github.ref_name }}',
              commit_sha: context.sha,
              // backend_type removed - using universal runtime configuration
              runtime_config_enabled: true
            };

            console.log('📦 Dispatch payload for react-frontend:', JSON.stringify(payload, null, 2));

            // Validate payload before sending
            const requiredFields = ['app_name', 'project_id', 'environment', 'docker_image', 'docker_tag'];
            for (const field of requiredFields) {
              if (!payload[field] || payload[field] === '') {
                throw new Error(`Required field '${field}' is missing or empty`);
              }
            }
            // Validate project_id format (must be lowercase alphanumeric with hyphens)
            if (!/^[a-z0-9-]+$/.test(payload.project_id)) {
              throw new Error(`Invalid project_id format: ${payload.project_id}. Must be lowercase alphanumeric with hyphens only.`);
            }

            // Validate environment
            if (!['dev', 'staging', 'production'].includes(payload.environment)) {
              throw new Error(`Invalid environment: ${payload.environment}. Must be dev, staging, or production.`);
            }

            try {
              await github.rest.repos.createDispatchEvent({
                owner: 'ChidhagniConsulting',
                repo: 'gitops-argocd-apps',
                event_type: 'deploy-to-argocd',
                client_payload: payload
              });

              console.log(`✅ GitOps deployment triggered successfully!`);
              console.log(`📱 App: AI React Frontend (ai-react-frontend)`);
              console.log(`🌍 Environment: ${environment}`);
              console.log(`🐳 Universal Docker image: registry.digitalocean.com/doks-registry/ai-react-frontend:${dockerTag}`);
              console.log(`⚙️  Runtime config: Backend URL will be configured via ConfigMap`);
              console.log(`🌿 Source branch: ${{ github.ref_name }}`);
              console.log(`📝 Commit SHA: ${context.sha}`);
              console.log(`🔗 Monitor deployment: https://github.com/ChidhagniConsulting/gitops-argocd-apps/actions`);
            } catch (error) {
              console.error('❌ Failed to trigger GitOps deployment:', error);
              console.error('Error details:', error.message);
              if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
              }
              throw error;
            }