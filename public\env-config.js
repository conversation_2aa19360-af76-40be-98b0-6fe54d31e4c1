// Runtime Environment Configuration
// This file can be dynamically updated at runtime without rebuilding the Docker image
// It will be mounted from a Kubernetes ConfigMap in production environments

window._env_ = {
  // Backend Configuration
  REACT_APP_BACKEND_URL: "http://localhost:8080", // Default Spring Boot URL (overwritten in K8s)
  REACT_APP_CURRENT_BACKEND: "spring", // Current backend type: spring, django, or nest
  
  // Environment Configuration
  REACT_APP_ENVIRONMENT: "development", // Environment: development, dev, staging, prod
  REACT_APP_API_VERSION: "v1", // API version
  
  // Service Configuration (for Kubernetes environments)
  REACT_APP_SERVICE_NAME: "ai-spring-backend-service",
  REACT_APP_BACKEND_NAMESPACE: "ai-spring-backend-dev",
  
  // OAuth Configuration
  REACT_APP_GOOGLE_OAUTH_URL: "http://localhost:8080/oauth2/authorize/google?redirect_uri=http://localhost:3000/oauth2/redirect",
  
  // Additional Configuration
  REACT_APP_PORT: "3000",
  REACT_APP_LAST_UPDATED: new Date().toISOString(),
  
  // Feature Flags
  REACT_APP_USE_RUNTIME_CONFIG: "true",
  REACT_APP_DEBUG_MODE: "false"
};

// Alternative backend configurations for easy switching
// Uncomment the desired backend configuration and comment out the current one

// Spring Boot Backend Configuration (Current)
/*
window._env_ = {
  REACT_APP_BACKEND_URL: "http://*************:8080",
  REACT_APP_CURRENT_BACKEND: "spring",
  REACT_APP_ENVIRONMENT: "dev",
  REACT_APP_API_VERSION: "v1",
  REACT_APP_SERVICE_NAME: "ai-spring-backend-service",
  REACT_APP_BACKEND_NAMESPACE: "ai-spring-backend-dev",
  REACT_APP_GOOGLE_OAUTH_URL: "http://*************:8080/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect",
  REACT_APP_PORT: "3000",
  REACT_APP_LAST_UPDATED: new Date().toISOString(),
  REACT_APP_USE_RUNTIME_CONFIG: "true",
  REACT_APP_DEBUG_MODE: "false"
};
*/

// Django Backend Configuration
/*
window._env_ = {
  REACT_APP_BACKEND_URL: "http://152.42.156.72:8000",
  REACT_APP_CURRENT_BACKEND: "django",
  REACT_APP_ENVIRONMENT: "dev",
  REACT_APP_API_VERSION: "v1",
  REACT_APP_SERVICE_NAME: "ai-django-backend-service",
  REACT_APP_BACKEND_NAMESPACE: "ai-django-backend-dev",
  REACT_APP_GOOGLE_OAUTH_URL: "http://152.42.156.72:8000/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect",
  REACT_APP_PORT: "3000",
  REACT_APP_LAST_UPDATED: new Date().toISOString(),
  REACT_APP_USE_RUNTIME_CONFIG: "true",
  REACT_APP_DEBUG_MODE: "false"
};
*/

// NestJS Backend Configuration
/*
window._env_ = {
  REACT_APP_BACKEND_URL: "http://174.138.121.78:3000",
  REACT_APP_CURRENT_BACKEND: "nest",
  REACT_APP_ENVIRONMENT: "dev",
  REACT_APP_API_VERSION: "v1",
  REACT_APP_SERVICE_NAME: "ai-nest-backend-service",
  REACT_APP_BACKEND_NAMESPACE: "ai-nest-backend-dev",
  REACT_APP_GOOGLE_OAUTH_URL: "http://174.138.121.78:3000/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect",
  REACT_APP_PORT: "3000",
  REACT_APP_LAST_UPDATED: new Date().toISOString(),
  REACT_APP_USE_RUNTIME_CONFIG: "true",
  REACT_APP_DEBUG_MODE: "false"
};
*/

// Debug information (only in development)
if (window._env_.REACT_APP_DEBUG_MODE === "true" || window._env_.REACT_APP_ENVIRONMENT === "development") {
  console.log("🔧 Runtime Environment Configuration Loaded:", window._env_);
  console.log("🚀 Current Backend:", window._env_.REACT_APP_CURRENT_BACKEND);
  console.log("🌐 Backend URL:", window._env_.REACT_APP_BACKEND_URL);
  console.log("📅 Last Updated:", window._env_.REACT_APP_LAST_UPDATED);
}
