# 🔄 Backend Switching - Frontend Changes

## Overview
This document outlines the changes required in the `ai-react-frontend` repository to enable dynamic backend URL switching using Kubernetes ConfigMaps.

## 🎯 Objective
Enable the React frontend to dynamically load backend URLs from a Kubernetes ConfigMap without rebuilding the Docker image.

---

## 📁 Current Project Structure Analysis

Based on the current codebase, the project uses:
- **Build Tool**: Vite with TypeScript
- **Backend Modes**: Spring, Nest, Django (via `package.json` scripts)
- **Environment Management**: Multiple `.env` files with structured naming
- **Testing**: Jest with comprehensive test coverage
- **CI/CD**: GitHub Actions with SonarQube and Semgrep integration

---

## 📝 Current Environment Configuration

### Current Environment Files Structure
The project uses a structured naming convention:
```
.env.spring              # Local Spring development
.env.nest                # Local Nest development  
.env.django              # Local Django development
.env.dev.spring          # Dev environment Spring
.env.dev.nest            # Dev environment Nest
.env.dev.django          # Dev environment Django
.env.staging.spring      # Staging environment Spring
.env.staging.nest        # Staging environment Nest
.env.staging.django      # Staging environment Django
.env.prod.spring         # Production environment Spring
.env.prod.nest           # Production environment Nest
.env.prod.django         # Production environment Django
```

### Current Environment Variables Structure
Based on `.env.dev.spring`:
```env
VITE_APP_ENV=dev
VITE_APP_API_URL=http://*************:8080
VITE_APP_PORT=3000
VITE_APP_GOOGLE_OAUTH_URL=http://*************:8080/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect
VITE_APP_SERVICE_NAME=ai-spring-backend-service
VITE_APP_BACKEND_NAMESPACE=ai-spring-backend-dev
```

---

## 📁 Files to Create

### 1. `src/services/configService.ts`
**Purpose**: Service to load runtime configuration from ConfigMap

```typescript
interface RuntimeConfig {
  currentBackend: 'spring' | 'nest' | 'django';
  backendUrl: string;
  environment: string;
  serviceName?: string;
  namespace?: string;
  apiVersion?: string;
}

class ConfigService {
  private config: RuntimeConfig | null = null;
  private readonly FALLBACK_URLS = {
    spring: 'http://localhost:8080',
    nest: 'http://localhost:3000', 
    django: 'http://localhost:8000'
  };

  async loadConfig(): Promise<RuntimeConfig> {
    try {
      const response = await fetch('/api/config');
      if (response.ok) {
        this.config = await response.json();
        return this.config;
      }
    } catch (error) {
      console.warn('Failed to load runtime config, using fallback');
    }

    return this.getFallbackConfig();
  }

  private getFallbackConfig(): RuntimeConfig {
    const backendType = this.getBackendFromEnv();
    
    return {
      currentBackend: backendType,
      backendUrl: import.meta.env.VITE_APP_API_URL || this.FALLBACK_URLS[backendType],
      environment: import.meta.env.VITE_APP_ENV || 'dev',
      serviceName: import.meta.env.VITE_APP_SERVICE_NAME,
      namespace: import.meta.env.VITE_APP_BACKEND_NAMESPACE,
      apiVersion: import.meta.env.VITE_APP_API_VERSION || 'v1'
    };
  }

  private getBackendFromEnv(): 'spring' | 'nest' | 'django' {
    const serviceName = import.meta.env.VITE_APP_SERVICE_NAME || '';
    if (serviceName.includes('spring')) return 'spring';
    if (serviceName.includes('nest')) return 'nest';
    if (serviceName.includes('django')) return 'django';
    return 'spring'; // default
  }

  async getCurrentBackendUrl(): Promise<string> {
    if (!this.config) {
      await this.loadConfig();
    }
    return this.config?.backendUrl || this.FALLBACK_URLS.spring;
  }

  async getCurrentBackend(): Promise<string> {
    if (!this.config) {
      await this.loadConfig();
    }
    return this.config?.currentBackend || 'spring';
  }
}

export const configService = new ConfigService();

// New runtime configuration access
export const getApiBaseUrl = async (): Promise<string> => {
  try {
    return await configService.getCurrentBackendUrl();
  } catch {
    return import.meta.env.VITE_APP_API_URL || 'http://localhost:8080';
  }
};

export const getCurrentBackend = async (): Promise<string> => {
  try {
    return await configService.getCurrentBackend();
  } catch {
    const serviceName = import.meta.env.VITE_APP_SERVICE_NAME || '';
    if (serviceName.includes('spring')) return 'spring';
    if (serviceName.includes('nest')) return 'nest';
    if (serviceName.includes('django')) return 'django';
    return 'spring';
  }
};

// Environment-specific configurations
export const isDevelopment = import.meta.env.DEV;
export const isProduction = import.meta.env.PROD;
export const isKubernetes = import.meta.env.VITE_APP_ENV === 'dev' || import.meta.env.VITE_APP_ENV === 'staging' || import.meta.env.VITE_APP_ENV === 'prod';
```

### 2. `src/config/env.ts`
**Purpose**: Centralized environment configuration

```typescript
// Current environment detection
export const getCurrentEnvironment = () => {
  return import.meta.env.VITE_APP_ENV || 'dev';
};

// Backend service configuration
export const getServiceConfig = () => {
  return {
    serviceName: import.meta.env.VITE_APP_SERVICE_NAME,
    namespace: import.meta.env.VITE_APP_BACKEND_NAMESPACE,
    port: import.meta.env.VITE_APP_PORT || 3000
  };
};

// API configuration
export const getApiConfig = () => {
  return {
    baseUrl: import.meta.env.VITE_APP_API_URL,
    oauthUrl: import.meta.env.VITE_APP_GOOGLE_OAUTH_URL
  };
};

// Environment checks
export const isDevEnvironment = getCurrentEnvironment() === 'dev';
export const isStagingEnvironment = getCurrentEnvironment() === 'staging';
export const isProdEnvironment = getCurrentEnvironment() === 'prod';
export const isKubernetesEnvironment = isDevEnvironment || isStagingEnvironment || isProdEnvironment;
```

### 3. `src/hooks/useBackendConfig.ts`
**Purpose**: React hook for backend configuration

```typescript
import { useState, useEffect } from 'react';
import { configService, type RuntimeConfig } from '../services/configService';

export const useBackendConfig = () => {
  const [config, setConfig] = useState<RuntimeConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadConfig = async () => {
      try {
        setLoading(true);
        const runtimeConfig = await configService.getConfig();
        setConfig(runtimeConfig);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load configuration');
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, []);

  const refreshConfig = async () => {
    try {
      setLoading(true);
      const runtimeConfig = await configService.loadConfig();
      setConfig(runtimeConfig);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh configuration');
    } finally {
      setLoading(false);
    }
  };

  return {
    config,
    loading,
    error,
    refreshConfig,
    currentBackend: config?.currentBackend || 'spring',
    backendUrl: config?.backendUrl || 'http://localhost:8080'
  };
};
```

---

## 📝 Files to Modify

### 1. `nginx.conf`
**Changes**: Add config endpoint to serve ConfigMap data

**Add this location block**:
```nginx
# Serve config from mounted ConfigMap
location /api/config {
    alias /etc/nginx/config/;
    try_files /runtime-config.json =404;
    add_header Content-Type application/json;
    add_header Access-Control-Allow-Origin *;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}
```

**Complete nginx.conf**:
```nginx
server {
    listen 3000;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Serve config from mounted ConfigMap
    location /api/config {
        alias /etc/nginx/config/;
        try_files /runtime-config.json =404;
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin *;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Static assets with caching
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # React app
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
}
```

### 2. Update `package.json` Scripts
**Add new scripts for runtime configuration**:

```json
{
  "scripts": {
    "config:check": "node scripts/check-runtime-config.js",
    "config:validate": "node scripts/validate-backend-config.js",
    "dev:runtime": "vite --mode runtime",
    "build:runtime": "vite build --mode runtime"
  }
}
```

### 3. Create `scripts/check-runtime-config.js`
**Purpose**: Script to validate runtime configuration

```javascript
const fs = require('fs');
const path = require('path');

const CONFIG_PATH = path.join(__dirname, '..', 'public', 'runtime-config.json');

function checkRuntimeConfig() {
  console.log('🔍 Checking runtime configuration...');
  
  if (!fs.existsSync(CONFIG_PATH)) {
    console.log('⚠️  Runtime config not found at:', CONFIG_PATH);
    console.log('📝 Creating default runtime config...');
    
    const defaultConfig = {
      currentBackend: 'spring',
      backendUrl: 'http://localhost:8080',
      environment: 'development',
      apiVersion: 'v1'
    };
    
    fs.writeFileSync(CONFIG_PATH, JSON.stringify(defaultConfig, null, 2));
    console.log('✅ Default runtime config created');
    return;
  }
  
  try {
    const config = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
    console.log('✅ Runtime config found:');
    console.log('   Backend:', config.currentBackend);
    console.log('   URL:', config.backendUrl);
    console.log('   Environment:', config.environment);
  } catch (error) {
    console.error('❌ Invalid runtime config JSON:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  checkRuntimeConfig();
}

module.exports = { checkRuntimeConfig };
```

### 4. Update API Service Files
**Example for existing API services**:

```typescript
// src/services/apiService.ts (if exists)
import { getApiBaseUrl } from '../config/env';

class ApiService {
  private baseUrl: string = '';

  async initialize() {
    this.baseUrl = await getApiBaseUrl();
  }

  async get(endpoint: string) {
    if (!this.baseUrl) await this.initialize();
    const response = await fetch(`${this.baseUrl}${endpoint}`);
    return response.json();
  }

  async post(endpoint: string, data: any) {
    if (!this.baseUrl) await this.initialize();
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    return response.json();
  }
}

export const apiService = new ApiService();
```

---

## 🔧 Integration with Current Environment System

### 1. Update Vite Configuration
**Add runtime mode support in `vite.config.ts`**:

```typescript
export default defineConfig(({ mode }) => {
  // Load env file based on mode
  const env = loadEnv(mode, process.cwd(), '');
  
  // Handle new environment file structure
  const envFile = mode.includes('.') ? mode : `${mode}`;
  
  return {
    server: {
      host: "::",
      port: 3000,
    },
    plugins: [
      react(),
      mode === 'development' && componentTagger(),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    define: {
      __APP_ENV__: JSON.stringify(env.VITE_APP_ENV || 'dev'),
      __SERVICE_NAME__: JSON.stringify(env.VITE_APP_SERVICE_NAME || ''),
      __BACKEND_NAMESPACE__: JSON.stringify(env.VITE_APP_BACKEND_NAMESPACE || ''),
    },
  };
});
```

### 2. Package.json Scripts Update
**Add scripts for new environment structure**:

```json
{
  "scripts": {
    "dev:spring": "vite --mode spring",
    "dev:nest": "vite --mode nest",
    "dev:django": "vite --mode django",
    "dev:spring:dev": "vite --mode dev.spring",
    "dev:nest:dev": "vite --mode dev.nest",
    "dev:django:dev": "vite --mode dev.django",
    "build:dev.spring": "vite build --mode dev.spring",
    "build:dev.nest": "vite build --mode dev.nest",
    "build:dev.django": "vite build --mode dev.django",
    "build:staging.spring": "vite build --mode staging.spring",
    "build:staging.nest": "vite build --mode staging.nest",
    "build:staging.django": "vite build --mode staging.django",
    "build:prod.spring": "vite build --mode prod.spring",
    "build:prod.nest": "vite build --mode prod.nest",
    "build:prod.django": "vite build --mode prod.django"
  }
}
```

---

## 📋 Environment File Examples

### Development Environment (.env.dev.spring)
```env
VITE_APP_ENV=dev
VITE_APP_API_URL=http://*************:8080
VITE_APP_PORT=3000
VITE_APP_GOOGLE_OAUTH_URL=http://*************:8080/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect
VITE_APP_SERVICE_NAME=ai-spring-backend-service
VITE_APP_BACKEND_NAMESPACE=ai-spring-backend-dev
```

### Staging Environment (.env.staging.spring)
```env
VITE_APP_ENV=staging
VITE_APP_API_URL=http://ai-spring-backend-service.ai-spring-backend-staging.svc.cluster.local:8080
VITE_APP_PORT=3000
VITE_APP_GOOGLE_OAUTH_URL=http://ai-spring-backend-service.ai-spring-backend-staging.svc.cluster.local:8080/oauth2/authorize/google?redirect_uri=http://ai-react-frontend-service.ai-react-frontend-staging.svc.cluster.local:3000/oauth2/redirect
VITE_APP_SERVICE_NAME=ai-spring-backend-service
VITE_APP_BACKEND_NAMESPACE=ai-spring-backend-staging
```

### Production Environment (.env.prod.spring)
```env
VITE_APP_ENV=prod
VITE_APP_API_URL=http://ai-spring-backend-service.ai-spring-backend-prod.svc.cluster.local:8080
VITE_APP_PORT=3000
VITE_APP_GOOGLE_OAUTH_URL=http://ai-spring-backend-service.ai-spring-backend-prod.svc.cluster.local:8080/oauth2/authorize/google?redirect_uri=http://ai-react-frontend-service.ai-react-frontend-prod.svc.cluster.local:3000/oauth2/redirect
VITE_APP_SERVICE_NAME=ai-spring-backend-service
VITE_APP_BACKEND_NAMESPACE=ai-spring-backend-prod
```

---

## ✅ Benefits

1. **Structured Environment Management**: Clear separation between local, dev, staging, and production
2. **Kubernetes Integration**: Built-in service name and namespace configuration
3. **Backward Compatibility**: Works with existing Vite configuration
4. **Runtime Configuration**: Dynamic backend switching without rebuilds
5. **Environment-Specific URLs**: Proper internal service communication for Kubernetes environments

---

## 📋 Implementation Checklist

### Core Files
- [ ] Create `src/services/configService.ts`
- [ ] Create `src/config/env.ts`
- [ ] Create `src/hooks/useBackendConfig.ts`
- [ ] Update `vite.config.ts` with new environment structure
- [ ] Update `package.json` scripts for new naming convention
- [ ] Test all environment modes work correctly

### Environment Validation
- [ ] Verify all `.env.*` files have correct structure
- [ ] Test local development with `.env.spring`, `.env.nest`, `.env.django`
- [ ] Test dev environment with `.env.dev.*` files
- [ ] Test staging environment with `.env.staging.*` files
- [ ] Test production environment with `.env.prod.*` files

---

## 🚨 Important Notes

1. **Environment Compatibility**: The solution maintains compatibility with existing `.env` files and `package.json` scripts
2. **Testing Integration**: All existing Jest tests (362 tests across 25 suites) should continue to pass
3. **CI/CD Pipeline**: SonarQube and Semgrep scans should pass without issues
4. **Nginx Configuration**: The `/api/config` endpoint must be properly configured for ConfigMap access
5. **Fallback Behavior**: Service gracefully falls back to environment variables if ConfigMap is unavailable


