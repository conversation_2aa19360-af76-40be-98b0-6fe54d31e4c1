# 🚀 New Approach - GitOps ArgoCD Apps Changes

## Overview
This document outlines the **NEW APPROACH** for implementing runtime environment configuration in the `gitops-argocd-apps` repository. This approach uses a JavaScript configuration file (`env-config.js`) mounted from a Kubernetes ConfigMap.

## 🎯 Current GitOps Structure
```
GITOPS-ARGOCD-APPS/
│
├── .github/
├── .idea/
├── ai-django-backend/
├── ai-nest-backend/
├── ai-react-frontend/
│
└── argocd/
    ├── application.yaml
    ├── project.yaml
    └── k8s/
        ├── configmap.yaml
        ├── deployment.yaml
        ├── namespace.yaml
        ├── nginx-configmap.yaml
        ├── secret.yaml
        └── service.yaml
```

---

## 📁 Files to Create/Update

### 1. Create Frontend Environment ConfigMap for Dev Environment

**File**: `argocd/k8s/configmap.yaml`
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-env
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: environment-config
    managed-by: argocd
    environment: dev
  annotations:
    argocd.argoproj.io/sync-wave: "1"
    description: "Runtime environment configuration for dev environment - all backends deployed"
data:
  env-config.js: |
    window._env_ = {
      // Current active backend (change this to switch backends)
      REACT_APP_BACKEND_URL: "http://*************:8080",  // Spring Boot (default)
      REACT_APP_CURRENT_BACKEND: "spring",

      // Environment configuration
      REACT_APP_ENVIRONMENT: "dev",
      REACT_APP_API_VERSION: "v1",
      REACT_APP_LAST_UPDATED: "2024-01-01T00:00:00Z",

      // Service configuration for dev environment
      REACT_APP_SERVICE_NAME: "ai-spring-backend-service",
      REACT_APP_BACKEND_NAMESPACE: "ai-spring-backend-dev",

      // OAuth configuration
      REACT_APP_GOOGLE_OAUTH_URL: "http://*************:8080/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect",

      // Feature flags
      REACT_APP_USE_RUNTIME_CONFIG: "true",
      REACT_APP_DEBUG_MODE: "true"
    };

    // Available backend configurations for dev environment:
    //
    // Spring Boot Backend:
    // REACT_APP_BACKEND_URL: "http://*************:8080"
    // REACT_APP_CURRENT_BACKEND: "spring"
    // REACT_APP_SERVICE_NAME: "ai-spring-backend-service"
    // REACT_APP_BACKEND_NAMESPACE: "ai-spring-backend-dev"
    //
    // Django Backend:
    // REACT_APP_BACKEND_URL: "http://152.42.156.72:8000"
    // REACT_APP_CURRENT_BACKEND: "django"
    // REACT_APP_SERVICE_NAME: "ai-django-backend-service"
    // REACT_APP_BACKEND_NAMESPACE: "ai-django-backend-dev"
    //
    // Nest Backend:
    // REACT_APP_BACKEND_URL: "http://174.138.121.78:3000"
    // REACT_APP_CURRENT_BACKEND: "nest"
    // REACT_APP_SERVICE_NAME: "ai-nest-backend-service"
    // REACT_APP_BACKEND_NAMESPACE: "ai-nest-backend-dev"
```

### 2. Update Deployment to Mount ConfigMap

**File**: `argocd/k8s/deployment.yaml`
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    version: v1.0.0
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-react-frontend
  template:
    metadata:
      labels:
        app: ai-react-frontend
    spec:
      containers:
        - name: ai-react-frontend
          image: your-registry/ai-react-frontend:latest
          ports:
            - containerPort: 80
          env:
            - name: VITE_APP_ENV
              value: "dev"
            - name: VITE_APP_SERVICE_NAME
              value: "ai-spring-backend-service"
            - name: VITE_APP_BACKEND_NAMESPACE
              value: "ai-spring-backend-dev"
          volumeMounts:
            # Mount env-config.js to the public folder
            - name: env-config-volume
              mountPath: /usr/share/nginx/html/env-config.js
              subPath: env-config.js
              readOnly: true
            # Mount nginx configuration
            - name: nginx-config-volume
              mountPath: /etc/nginx/conf.d
              readOnly: true
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "200m"
      volumes:
        # Volume for runtime environment configuration
        - name: env-config-volume
          configMap:
            name: ai-react-frontend-env
        # Volume for nginx configuration
        - name: nginx-config-volume
          configMap:
            name: ai-react-frontend-nginx-config
      restartPolicy: Always
```

### 3. Update Nginx Configuration

**File**: `argocd/k8s/nginx-configmap.yaml`
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-nginx-config
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: nginx-config
data:
  default.conf: |
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # Serve env-config.js with no caching
        location /env-config.js {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            add_header Access-Control-Allow-Origin "*";
        }

        # Static assets with caching
        location /assets/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # React app - handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
```

---

## 🔄 Backend Switching Configurations

### Spring Boot Backend Configuration
```yaml
# argocd/k8s/configmap.yaml
data:
  env-config.js: |
    window._env_ = {
      REACT_APP_BACKEND_URL: "http://*************:8080",
      REACT_APP_CURRENT_BACKEND: "spring",
      REACT_APP_ENVIRONMENT: "dev",
      REACT_APP_API_VERSION: "v1"
    };
```

### Django Backend Configuration
```yaml
# argocd/k8s/configmap.yaml
data:
  env-config.js: |
    window._env_ = {
      REACT_APP_BACKEND_URL: "http://152.42.156.72:8000",
      REACT_APP_CURRENT_BACKEND: "django",
      REACT_APP_ENVIRONMENT: "dev",
      REACT_APP_API_VERSION: "v1"
    };
```

### Nest Backend Configuration
```yaml
# argocd/k8s/configmap.yaml
data:
  env-config.js: |
    window._env_ = {
      REACT_APP_BACKEND_URL: "http://174.138.121.78:3000",
      REACT_APP_CURRENT_BACKEND: "nest",
      REACT_APP_ENVIRONMENT: "dev",
      REACT_APP_API_VERSION: "v1"
    };
```

---

## 🚀 Backend Switching Process for Dev Environment

Since all backends (Spring, Django, Nest) are deployed in dev environment, you can easily switch between them:

### Method 1: Switch to Django Backend
```bash
# Update ConfigMap to point to Django backend
kubectl patch configmap ai-react-frontend-env -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "env-config.js": "window._env_ = {\n  REACT_APP_BACKEND_URL: \"http://152.42.156.72:8000\",\n  REACT_APP_CURRENT_BACKEND: \"django\",\n  REACT_APP_ENVIRONMENT: \"dev\",\n  REACT_APP_API_VERSION: \"v1\",\n  REACT_APP_SERVICE_NAME: \"ai-django-backend-service\",\n  REACT_APP_BACKEND_NAMESPACE: \"ai-django-backend-dev\",\n  REACT_APP_GOOGLE_OAUTH_URL: \"http://152.42.156.72:8000/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect\",\n  REACT_APP_USE_RUNTIME_CONFIG: \"true\",\n  REACT_APP_DEBUG_MODE: \"true\",\n  REACT_APP_LAST_UPDATED: \"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"\n};"
  }
}'

# Restart deployment to pick up new configuration
kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev

echo "✅ Switched to Django backend (http://152.42.156.72:8000)"
```

### Method 2: Switch to Nest Backend
```bash
# Update ConfigMap to point to Nest backend
kubectl patch configmap ai-react-frontend-env -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "env-config.js": "window._env_ = {\n  REACT_APP_BACKEND_URL: \"http://174.138.121.78:3000\",\n  REACT_APP_CURRENT_BACKEND: \"nest\",\n  REACT_APP_ENVIRONMENT: \"dev\",\n  REACT_APP_API_VERSION: \"v1\",\n  REACT_APP_SERVICE_NAME: \"ai-nest-backend-service\",\n  REACT_APP_BACKEND_NAMESPACE: \"ai-nest-backend-dev\",\n  REACT_APP_GOOGLE_OAUTH_URL: \"http://174.138.121.78:3000/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect\",\n  REACT_APP_USE_RUNTIME_CONFIG: \"true\",\n  REACT_APP_DEBUG_MODE: \"true\",\n  REACT_APP_LAST_UPDATED: \"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"\n};"
  }
}'

# Restart deployment to pick up new configuration
kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev

echo "✅ Switched to Nest backend (http://174.138.121.78:3000)"
```

### Method 3: Switch back to Spring Backend
```bash
# Update ConfigMap to point to Spring backend
kubectl patch configmap ai-react-frontend-env -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "env-config.js": "window._env_ = {\n  REACT_APP_BACKEND_URL: \"http://*************:8080\",\n  REACT_APP_CURRENT_BACKEND: \"spring\",\n  REACT_APP_ENVIRONMENT: \"dev\",\n  REACT_APP_API_VERSION: \"v1\",\n  REACT_APP_SERVICE_NAME: \"ai-spring-backend-service\",\n  REACT_APP_BACKEND_NAMESPACE: \"ai-spring-backend-dev\",\n  REACT_APP_GOOGLE_OAUTH_URL: \"http://*************:8080/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect\",\n  REACT_APP_USE_RUNTIME_CONFIG: \"true\",\n  REACT_APP_DEBUG_MODE: \"true\",\n  REACT_APP_LAST_UPDATED: \"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"\n};"
  }
}'

# Restart deployment to pick up new configuration
kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev

echo "✅ Switched to Spring backend (http://*************:8080)"
```

### Method 2: GitOps Workflow (Recommended)
```bash
# 1. Update configmap.yaml in GitOps repository
cd gitops-argocd-apps
git checkout -b switch-to-django-backend

# 2. Edit argocd/k8s/configmap.yaml
# Change REACT_APP_BACKEND_URL to "http://152.42.156.72:8000"
# Change REACT_APP_CURRENT_BACKEND to "django"

# 3. Commit and push
git add argocd/k8s/configmap.yaml
git commit -m "feat: Switch frontend to Django backend

- Update REACT_APP_BACKEND_URL to http://152.42.156.72:8000
- Set REACT_APP_CURRENT_BACKEND to django
- Maintain dev environment configuration"

git push origin switch-to-django-backend

# 4. ArgoCD will automatically sync the changes
```

---

## 🧪 Testing and Verification

### 1. Verify Configuration Loading
```bash
# Check if ConfigMap is created
kubectl get configmap ai-react-frontend-env -n ai-react-frontend-dev

# View current configuration
kubectl get configmap ai-react-frontend-env -n ai-react-frontend-dev -o yaml

# Test frontend access
curl http://your-frontend-url/env-config.js
```

### 2. Test Backend Connectivity
```bash
# Test Spring Boot backend
curl http://*************:8080/actuator/health

# Test Django backend
curl http://152.42.156.72:8000/health

# Test Nest backend
curl http://174.138.121.78:3000/health
```

### 3. Verify Frontend Application
```bash
# Check deployment status
kubectl get deployment ai-react-frontend -n ai-react-frontend-dev

# Check pod logs
kubectl logs deployment/ai-react-frontend -n ai-react-frontend-dev

# Test frontend health
curl http://your-frontend-url/health
```

---

## ✅ Benefits of New Approach

1. **🚀 No Image Rebuild**: Switch backends without rebuilding Docker images
2. **⚡ Immediate Effect**: Configuration changes take effect after pod restart
3. **🔧 Simple Management**: Easy to update via kubectl or GitOps
4. **📊 Clear Visibility**: Configuration visible in browser dev tools
5. **🔄 GitOps Compatible**: Fully integrated with ArgoCD workflow
6. **🛡️ Production Ready**: Standard approach used by many React applications

---

## 📋 Implementation Checklist

### GitOps Repository Setup
- [ ] Create `argocd/k8s/configmap.yaml` with env-config.js
- [ ] Update `argocd/k8s/deployment.yaml` with volume mount
- [ ] Update `argocd/k8s/nginx-configmap.yaml` with proper headers
- [ ] Commit and push changes to GitOps repository

### ArgoCD Deployment
- [ ] Sync ArgoCD application
- [ ] Verify ConfigMap created successfully
- [ ] Verify deployment updated with new volume mount
- [ ] Test env-config.js accessibility from frontend

### Backend Switching Tests
- [ ] Test current Spring Boot backend configuration
- [ ] Test switch to Django backend
- [ ] Test switch to Nest backend
- [ ] Verify configuration changes reflect in browser

### Production Readiness
- [ ] Test in staging environment
- [ ] Validate security headers
- [ ] Verify caching policies
- [ ] Document operational procedures

This new approach provides a clean, standard way to handle runtime configuration in Kubernetes while maintaining full GitOps compatibility.
