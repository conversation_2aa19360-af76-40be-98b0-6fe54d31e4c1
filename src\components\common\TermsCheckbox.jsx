import React from 'react';
import { FormControlLabel, Checkbox, Typography, Link } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * Reusable terms and conditions checkbox component
 */
const TermsCheckbox = ({ 
  checked, 
  onChange, 
  disabled = false,
  error = false,
  termsUrl = "#",
  privacyUrl = "#",
  sx = {},
  ...props
}) => {
  return (
    <FormControlLabel
      control={
        <Checkbox
          checked={checked}
          onChange={onChange}
          disabled={disabled}
          size="small"
          color={error ? "error" : "primary"}
        />
      }
      label={
        <Typography variant="body2" sx={{ color: error ? '#f44336' : '#666', fontSize: '0.8rem' }}>
          I agree to the{' '}
          <Link href={termsUrl} sx={{ color: '#1976d2', textDecoration: 'none' }}>
            Terms of Service
          </Link>{' '}
          and{' '}
          <Link href={privacyUrl} sx={{ color: '#1976d2', textDecoration: 'none' }}>
            Privacy Policy
          </Link>
        </Typography>
      }
      sx={{ mb: 1.5, alignItems: 'flex-start', ...sx }}
      {...props}
    />
  );
};

TermsCheckbox.propTypes = {
  checked: PropTypes.bool.isRequired,
  onChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  error: PropTypes.bool,
  termsUrl: PropTypes.string,
  privacyUrl: PropTypes.string,
  sx: PropTypes.object,
};

export default TermsCheckbox;
