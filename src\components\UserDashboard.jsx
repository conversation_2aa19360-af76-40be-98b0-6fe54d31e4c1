import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Avatar,
  Chip,
  Card,
  CardContent,
  AppBar,
  Toolbar,
  IconButton,
  Badge,
  Menu,
  MenuItem,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  LogOut,
  User,
  CheckCircle,
  Settings,
  Bell,
  Menu as MenuIcon,
  BarChart3,
  Users,
  FileText,
  HelpCircle,
  Home,
  Activity,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import axios from 'axios';
import { getApiBaseUrl } from '../config/env';

const UserDashboard = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [mobileOpen, setMobileOpen] = useState(false);
  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null);

  // Get API URL from environment
  const API_BASE_URL = getApiBaseUrl();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleSettingsOpen = (event) => {
    setSettingsAnchorEl(event.currentTarget);
  };

  const handleSettingsClose = () => {
    setSettingsAnchorEl(null);
  };

  const handleNotificationsOpen = (event) => {
    setNotificationsAnchorEl(event.currentTarget);
  };

  const handleNotificationsClose = () => {
    setNotificationsAnchorEl(null);
  };

  const handleLogout = async () => {
    try {
      const sessionToken = localStorage.getItem('sessionToken');

      const fetchIpAddress = async () => {
        try {
          const res = await axios.get("https://api.ipify.org?format=json");
          return res.data.ip;
        } catch {
          return "";
        }
      };

      const ipAddress = await fetchIpAddress();
      const deviceDetails = navigator.userAgent;
      await axios.post(`${API_BASE_URL}/api/v1/logout`, {
        sessionToken: sessionToken,
        ipAddress: ipAddress,
        deviceDetails: deviceDetails
      }, {
        headers: {
          'Authorization': `Bearer ${sessionToken}`,
          'Content-Type': 'application/json'
        },
        validateStatus: () => true
      });

      localStorage.removeItem('userId');
      localStorage.removeItem('sessionToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('expiresAt');
      localStorage.removeItem('refreshExpiresAt');

      logout();
      toast.success('Logged out successfully');
      navigate('/');

    } catch (error) {
      localStorage.removeItem('userId');
      localStorage.removeItem('sessionToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('expiresAt');
      localStorage.removeItem('refreshExpiresAt');
      logout();
      navigate('/');
      toast.error('Logged out successfully');
    }
  };

  const drawerWidth = 260;

  const navigationItems = [
    { text: 'Analytics', icon: <BarChart3 size={20} />, path: '/analytics' },
    { text: 'Users', icon: <Users size={20} />, path: '/users' },
    { text: 'Projects', icon: <FileText size={20} />, path: '/projects' },
    { text: 'Calendar', icon: <Calendar size={20} />, path: '/calendar' },
    { text: 'Messages', icon: <MessageSquare size={20} />, path: '/messages' },
    { text: 'Activity', icon: <Activity size={20} />, path: '/activity' },
    { text: 'Help', icon: <HelpCircle size={20} />, path: '/help' },
  ];

  const notifications = [
    { id: 1, message: 'New user registered', time: '5 min ago', unread: true },
    { id: 2, message: 'System update completed', time: '1 hour ago', unread: true },
    { id: 3, message: 'Weekly report ready', time: '2 hours ago', unread: false },
    { id: 4, message: 'Password expires in 3 days', time: '1 day ago', unread: false },
  ];

  const unreadCount = notifications.filter(n => n.unread).length;

  const dashboardStats = [
    { title: 'Total Users', value: '1,234', change: '+12%', color: '#4caf50' },
    { title: 'Revenue', value: '$45,678', change: '+8%', color: '#2196f3' },
    { title: 'Active Projects', value: '89', change: '+5%', color: '#ff9800' },
    { title: 'Tasks Completed', value: '156', change: '+15%', color: '#9c27b0' },
  ];

  const drawer = (
    <Box>
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
        <Avatar sx={{ bgcolor: 'primary.main' }}>
          <Home size={20} />
        </Avatar>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Dashboard
        </Typography>
      </Box>
      <Divider />
      <List>
        {navigationItems.map((item) => (
          <ListItem 
            key={item.text}
            sx={{ 
              py: 1,
              mx: 1,
              borderRadius: 1,
              '&:hover': { bgcolor: 'action.hover' },
              cursor: 'pointer'
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.text}
              slotProps={{ primary: { fontSize: '0.9rem' } }}
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <AppBar
        position="fixed" 
        sx={{ 
          zIndex: theme.zIndex.drawer + 1,
          bgcolor: 'white',
          color: 'text.primary',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {isMobile && (
              <IconButton
                color="inherit"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
            )}
            <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
              MyApp Dashboard
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mr: 2 }}>
              <Avatar 
                src={user?.profilePicture}
                sx={{ width: 32, height: 32 }}
              >
                <User size={16} />
              </Avatar>
              <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                <Typography variant="body2" sx={{ fontWeight: 500, lineHeight: 1 }}>
                  {user?.name}
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  {user?.email}
                </Typography>
              </Box>
            </Box>


            <IconButton 
              onClick={handleNotificationsOpen}
              sx={{ color: 'text.primary' }}
            >
              <Badge badgeContent={unreadCount} color="error">
                <Bell size={20} />
              </Badge>
            </IconButton>


            <IconButton 
              onClick={handleSettingsOpen}
              sx={{ color: 'text.primary' }}
            >
              <Settings size={20} />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>


      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant={isMobile ? 'temporary' : 'permanent'}
          open={isMobile ? mobileOpen : true}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              mt: isMobile ? 0 : '64px',
              height: isMobile ? '100%' : 'calc(100% - 64px)',
              borderRight: '1px solid',
              borderColor: 'divider',
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>


      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 2,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: '64px',
          bgcolor: '#f8f9fa',
          minHeight: 'calc(100vh - 64px)',
        }}
      >
        <Container maxWidth="xl">

          <Box sx={{ mb: 3 }}>
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
              Welcome back, {user?.name}!
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Here's what's happening with your projects today.
            </Typography>
          </Box>


          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: { 
              xs: '1fr', 
              sm: 'repeat(2, 1fr)', 
              lg: 'repeat(4, 1fr)' 
            }, 
            gap: 2, 
            mb: 3 
          }}>
            {dashboardStats.map((stat, index) => (
              <Card key={index} sx={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                <CardContent sx={{ p: 2 }}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                    {stat.title}
                  </Typography>
                  <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                    {stat.value}
                  </Typography>
                  <Chip 
                    label={stat.change}
                    size="small"
                    sx={{ 
                      bgcolor: stat.color, 
                      color: 'white',
                      fontSize: '0.7rem',
                      height: 20
                    }}
                  />
                </CardContent>
              </Card>
            ))}
          </Box>


          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: { xs: '1fr', md: 'repeat(3, 1fr)' }, 
            gap: 2 
          }}>
            <Card sx={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <CheckCircle size={16} color="#4caf50" />
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#4caf50' }}>
                    Account Status
                  </Typography>
                </Box>
                <Chip
                  label="Active"
                  color="success"
                  variant="outlined"
                  size="small"
                  sx={{ fontSize: '0.7rem', height: 24 }}
                />
              </CardContent>
            </Card>

            <Card sx={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Login Method
                </Typography>
                <Chip
                  label={user?.id?.startsWith('google_') ? 'Google OAuth' : 'Email & Password'}
                  color="primary"
                  variant="outlined"
                  size="small"
                  sx={{ fontSize: '0.7rem', height: 24 }}
                />
              </CardContent>
            </Card>

            <Card sx={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Security
                </Typography>
                <Chip
                  label="2FA Enabled"
                  color="success"
                  variant="outlined"
                  size="small"
                  sx={{ fontSize: '0.7rem', height: 24 }}
                />
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Box>


      <Menu
        anchorEl={settingsAnchorEl}
        open={Boolean(settingsAnchorEl)}
        onClose={handleSettingsClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => { handleSettingsClose(); navigate('/profile'); }}>
          <ListItemIcon><User size={16} /></ListItemIcon>
          Profile Settings
        </MenuItem>
       
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon><LogOut size={16} /></ListItemIcon>
          Sign Out
        </MenuItem>
      </Menu>


      <Menu
        anchorEl={notificationsAnchorEl}
        open={Boolean(notificationsAnchorEl)}
        onClose={handleNotificationsClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        slotProps={{ paper: { sx: { width: 320, maxHeight: 400 } } }}
      >
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Notifications
          </Typography>
        </Box>
        {notifications.map((notification) => (
          <MenuItem 
            key={notification.id} 
            onClick={handleNotificationsClose}
            sx={{ 
              py: 1.5,
              borderLeft: notification.unread ? '3px solid #2196f3' : '3px solid transparent'
            }}
          >
            <Box>
              <Typography 
                variant="body2" 
                sx={{ 
                  fontWeight: notification.unread ? 600 : 400,
                  mb: 0.5 
                }}
              >
                {notification.message}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                {notification.time}
              </Typography>
            </Box>
          </MenuItem>
        ))}
        <Divider />
        <MenuItem onClick={handleNotificationsClose} sx={{ justifyContent: 'center' }}>
          <Typography variant="body2" color="primary">
            View All Notifications
          </Typography>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default UserDashboard;
