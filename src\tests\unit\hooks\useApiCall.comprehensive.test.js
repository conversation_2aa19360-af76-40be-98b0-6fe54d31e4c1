import { renderHook, act } from '@testing-library/react';
import { toast } from 'sonner';
import useApiCall from '../../../hooks/useApiCall';

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

describe('useApiCall - Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    toast.success.mockClear();
    toast.error.mockClear();
  });

  describe('Initial State', () => {
    it('returns correct initial state', () => {
      const { result } = renderHook(() => useApiCall());

      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('');
      expect(result.current.success).toBe(false);
      expect(typeof result.current.execute).toBe('function');
      expect(typeof result.current.reset).toBe('function');
      expect(typeof result.current.setError).toBe('function');
      expect(typeof result.current.setSuccess).toBe('function');
    });

    it('provides all expected properties', () => {
      const { result } = renderHook(() => useApiCall());

      const expectedProperties = [
        'loading',
        'error',
        'success',
        'execute',
        'reset',
        'setError',
        'setSuccess'
      ];

      expectedProperties.forEach(prop => {
        expect(result.current).toHaveProperty(prop);
      });
    });
  });

  describe('Successful API Calls', () => {
    it('handles successful API call with default options', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });

      await act(async () => {
        const response = await result.current.execute(mockApiCall);
        expect(response).toEqual({ data: 'success' });
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('');
      expect(result.current.success).toBe(true);
      expect(mockApiCall).toHaveBeenCalledTimes(1);
    });

    it('shows success toast when successMessage is provided', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });

      await act(async () => {
        await result.current.execute(mockApiCall, {
          successMessage: 'Operation completed successfully!'
        });
      });

      expect(toast.success).toHaveBeenCalledWith('Operation completed successfully!');
      expect(result.current.success).toBe(true);
    });

    it('calls onSuccess callback when provided', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });
      const mockOnSuccess = jest.fn();

      await act(async () => {
        await result.current.execute(mockApiCall, {
          onSuccess: mockOnSuccess
        });
      });

      expect(mockOnSuccess).toHaveBeenCalledWith({ data: 'success' });
      expect(result.current.success).toBe(true);
    });

    it('does not show toast when showToast is false', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });

      await act(async () => {
        await result.current.execute(mockApiCall, {
          successMessage: 'Success!',
          showToast: false
        });
      });

      expect(toast.success).not.toHaveBeenCalled();
      expect(result.current.success).toBe(true);
    });

    it('does not reset state when resetOnStart is false', async () => {
      const { result } = renderHook(() => useApiCall());
      
      // Set initial error state
      act(() => {
        result.current.setError('Previous error');
        result.current.setSuccess(true);
      });

      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });

      await act(async () => {
        await result.current.execute(mockApiCall, {
          resetOnStart: false
        });
      });

      expect(result.current.success).toBe(true);
      expect(result.current.error).toBe('Previous error'); // Should not be reset
    });
  });

  describe('Failed API Calls', () => {
    it('handles API call failure with default error handling', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockError = new Error('API call failed');
      const mockApiCall = jest.fn().mockRejectedValue(mockError);

      await act(async () => {
        await result.current.execute(mockApiCall);
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('API call failed');
      expect(result.current.success).toBe(false);
      expect(toast.error).toHaveBeenCalledWith('API call failed');
    });

    it('uses custom error message when provided', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockError = new Error('Original error');
      const mockApiCall = jest.fn().mockRejectedValue(mockError);

      await act(async () => {
        await result.current.execute(mockApiCall, {
          errorMessage: 'Custom error message'
        });
      });

      expect(result.current.error).toBe('Custom error message');
      expect(toast.error).toHaveBeenCalledWith('Custom error message');
    });

    it('calls onError callback when provided', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockError = new Error('API call failed');
      const mockApiCall = jest.fn().mockRejectedValue(mockError);
      const mockOnError = jest.fn();

      await act(async () => {
        await result.current.execute(mockApiCall, {
          onError: mockOnError
        });
      });

      expect(mockOnError).toHaveBeenCalledWith(mockError);
      expect(result.current.error).toBe('API call failed');
    });

    it('handles error without message', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockError = {}; // Error without message
      const mockApiCall = jest.fn().mockRejectedValue(mockError);

      await act(async () => {
        await result.current.execute(mockApiCall);
      });

      expect(result.current.error).toBe('Something went wrong. Please try again.');
      expect(toast.error).toHaveBeenCalledWith('Something went wrong. Please try again.');
    });

    it('does not show error toast when showToast is false', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockError = new Error('API call failed');
      const mockApiCall = jest.fn().mockRejectedValue(mockError);

      await act(async () => {
        await result.current.execute(mockApiCall, {
          showToast: false
        });
      });

      expect(toast.error).not.toHaveBeenCalled();
      expect(result.current.error).toBe('API call failed');
    });
  });

  describe('Loading State Management', () => {
    it('sets loading to true during API call', async () => {
      const { result } = renderHook(() => useApiCall());
      let resolveApiCall;
      const mockApiCall = jest.fn(() => new Promise(resolve => {
        resolveApiCall = resolve;
      }));

      act(() => {
        result.current.execute(mockApiCall);
      });

      expect(result.current.loading).toBe(true);

      await act(async () => {
        resolveApiCall({ data: 'success' });
      });

      expect(result.current.loading).toBe(false);
    });

    it('prevents multiple simultaneous calls', async () => {
      const { result } = renderHook(() => useApiCall());
      let resolveFirstCall;
      const mockApiCall = jest.fn(() => new Promise(resolve => {
        resolveFirstCall = resolve;
      }));

      // Start first call
      act(() => {
        result.current.execute(mockApiCall);
      });

      expect(result.current.loading).toBe(true);

      // Try to start second call while first is still running
      await act(async () => {
        await result.current.execute(mockApiCall);
      });

      // Should only be called once
      expect(mockApiCall).toHaveBeenCalledTimes(1);

      // Resolve first call
      await act(async () => {
        resolveFirstCall({ data: 'success' });
      });

      expect(result.current.loading).toBe(false);
    });

    it('allows new calls after previous call completes', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });

      // First call
      await act(async () => {
        await result.current.execute(mockApiCall);
      });

      expect(mockApiCall).toHaveBeenCalledTimes(1);
      expect(result.current.loading).toBe(false);

      // Second call should be allowed
      await act(async () => {
        await result.current.execute(mockApiCall);
      });

      expect(mockApiCall).toHaveBeenCalledTimes(2);
      expect(result.current.loading).toBe(false);
    });
  });

  describe('Reset Functionality', () => {
    it('resets all state to initial values', () => {
      const { result } = renderHook(() => useApiCall());

      // Set some state
      act(() => {
        result.current.setError('Some error');
        result.current.setSuccess(true);
      });

      expect(result.current.error).toBe('Some error');
      expect(result.current.success).toBe(true);

      // Reset
      act(() => {
        result.current.reset();
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('');
      expect(result.current.success).toBe(false);
    });

    it('allows new API calls after reset', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });

      // Make a call and then reset
      await act(async () => {
        await result.current.execute(mockApiCall);
      });

      act(() => {
        result.current.reset();
      });

      // Should be able to make another call
      await act(async () => {
        await result.current.execute(mockApiCall);
      });

      expect(mockApiCall).toHaveBeenCalledTimes(2);
      expect(result.current.success).toBe(true);
    });
  });

  describe('Manual State Setters', () => {
    it('allows manual error setting', () => {
      const { result } = renderHook(() => useApiCall());

      act(() => {
        result.current.setError('Manual error');
      });

      expect(result.current.error).toBe('Manual error');
    });

    it('allows manual success setting', () => {
      const { result } = renderHook(() => useApiCall());

      act(() => {
        result.current.setSuccess(true);
      });

      expect(result.current.success).toBe(true);
    });

    it('can update error and success independently', () => {
      const { result } = renderHook(() => useApiCall());

      act(() => {
        result.current.setError('Error message');
        result.current.setSuccess(true);
      });

      expect(result.current.error).toBe('Error message');
      expect(result.current.success).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('handles API call that returns undefined', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue(undefined);

      let returnValue;
      await act(async () => {
        returnValue = await result.current.execute(mockApiCall);
      });

      expect(returnValue).toBeUndefined();
      expect(result.current.success).toBe(true);
      expect(result.current.error).toBe('');
    });

    it('handles API call that returns null', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue(null);

      let returnValue;
      await act(async () => {
        returnValue = await result.current.execute(mockApiCall);
      });

      expect(returnValue).toBeNull();
      expect(result.current.success).toBe(true);
    });

    it('handles empty options object', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });

      await act(async () => {
        await result.current.execute(mockApiCall, {});
      });

      expect(result.current.success).toBe(true);
      expect(result.current.error).toBe('');
    });

    it('handles null options by using defaults', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });

      await act(async () => {
        await result.current.execute(mockApiCall);
      });

      expect(result.current.success).toBe(true);
      expect(result.current.error).toBe('');
    });

    it('maintains function reference stability', () => {
      const { result, rerender } = renderHook(() => useApiCall());

      const initialExecute = result.current.execute;
      const initialReset = result.current.reset;
      const initialSetError = result.current.setError;
      const initialSetSuccess = result.current.setSuccess;

      rerender();

      expect(result.current.execute).toBe(initialExecute);
      expect(result.current.reset).toBe(initialReset);
      expect(result.current.setError).toBe(initialSetError);
      expect(result.current.setSuccess).toBe(initialSetSuccess);
    });
  });

  describe('Complex Scenarios', () => {
    it('handles success with both callback and toast', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });
      const mockOnSuccess = jest.fn();

      await act(async () => {
        await result.current.execute(mockApiCall, {
          onSuccess: mockOnSuccess,
          successMessage: 'Great success!',
          showToast: true
        });
      });

      expect(mockOnSuccess).toHaveBeenCalledWith({ data: 'success' });
      expect(toast.success).toHaveBeenCalledWith('Great success!');
      expect(result.current.success).toBe(true);
    });

    it('handles error with both callback and custom message', async () => {
      const { result } = renderHook(() => useApiCall());
      const mockError = new Error('Original error');
      const mockApiCall = jest.fn().mockRejectedValue(mockError);
      const mockOnError = jest.fn();

      await act(async () => {
        await result.current.execute(mockApiCall, {
          onError: mockOnError,
          errorMessage: 'Custom error',
          showToast: true
        });
      });

      expect(mockOnError).toHaveBeenCalledWith(mockError);
      expect(toast.error).toHaveBeenCalledWith('Custom error');
      expect(result.current.error).toBe('Custom error');
    });
  });
});
