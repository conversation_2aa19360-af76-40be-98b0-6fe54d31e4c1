interface RuntimeConfig {
  currentBackend: 'spring' | 'nest' | 'django';
  backendUrl: string;
  environment: string;
  serviceName?: string;
  namespace?: string;
  apiVersion?: string;
}

class ConfigService {
  private config: RuntimeConfig | null = null;
  private readonly FALLBACK_URLS = {
    spring: 'http://localhost:8080',
    nest: 'http://localhost:3000', 
    django: 'http://localhost:8000'
  };

  async loadConfig(): Promise<RuntimeConfig> {
    // First try to load from window._env_ (runtime config)
    if (typeof window !== 'undefined' && window._env_) {
      this.config = {
        currentBackend: (window._env_.REACT_APP_CURRENT_BACKEND as 'spring' | 'nest' | 'django') || 'spring',
        backendUrl: window._env_.REACT_APP_BACKEND_URL || this.FALLBACK_URLS.spring,
        environment: window._env_.REACT_APP_ENVIRONMENT || 'development',
        serviceName: window._env_.REACT_APP_SERVICE_NAME,
        namespace: window._env_.REACT_APP_BACKEND_NAMESPACE,
        apiVersion: window._env_.REACT_APP_API_VERSION || 'v1'
      };

      if (window._env_.REACT_APP_DEBUG_MODE === 'true') {
        console.log('🔧 ConfigService: Loaded from window._env_:', this.config);
      }

      return this.config;
    }

    // Fallback to existing runtime-config.json approach
    try {
      const response = await fetch('/api/config');
      if (response.ok) {
        this.config = await response.json();
        console.log('🔧 ConfigService: Loaded from /api/config:', this.config);
        return this.config;
      }
    } catch (error) {
      console.warn('Failed to load runtime config from /api/config, using fallback');
    }

    // Final fallback to environment variables
    return this.getFallbackConfig();
  }

  private getFallbackConfig(): RuntimeConfig {
    const backendType = this.getBackendFromEnv();
    
    return {
      currentBackend: backendType,
      backendUrl: import.meta.env.VITE_APP_API_URL || this.FALLBACK_URLS[backendType],
      environment: import.meta.env.VITE_APP_ENV || 'dev',
      serviceName: import.meta.env.VITE_APP_SERVICE_NAME,
      namespace: import.meta.env.VITE_APP_BACKEND_NAMESPACE,
      apiVersion: import.meta.env.VITE_APP_API_VERSION || 'v1'
    };
  }

  private getBackendFromEnv(): 'spring' | 'nest' | 'django' {
    const serviceName = import.meta.env.VITE_APP_SERVICE_NAME || '';
    if (serviceName.includes('spring')) return 'spring';
    if (serviceName.includes('nest')) return 'nest';
    if (serviceName.includes('django')) return 'django';
    return 'spring'; // default
  }

  async getCurrentBackendUrl(): Promise<string> {
    if (!this.config) {
      await this.loadConfig();
    }
    return this.config?.backendUrl || this.FALLBACK_URLS.spring;
  }

  async getCurrentBackend(): Promise<string> {
    if (!this.config) {
      await this.loadConfig();
    }
    return this.config?.currentBackend || 'spring';
  }

  async getConfig(): Promise<RuntimeConfig> {
    if (!this.config) {
      await this.loadConfig();
    }
    return this.config || this.getFallbackConfig();
  }
}

export const configService = new ConfigService();

// New runtime configuration access
export const getApiBaseUrl = async (): Promise<string> => {
  try {
    // Use window._env_ if available (runtime config)
    if (typeof window !== 'undefined' && window._env_?.REACT_APP_BACKEND_URL) {
      return window._env_.REACT_APP_BACKEND_URL;
    }

    // Fallback to service
    return await configService.getCurrentBackendUrl();
  } catch {
    return import.meta.env.VITE_APP_API_URL || 'http://localhost:8080';
  }
};

export const getCurrentBackend = async (): Promise<string> => {
  try {
    // Use window._env_ if available (runtime config)
    if (typeof window !== 'undefined' && window._env_?.REACT_APP_CURRENT_BACKEND) {
      return window._env_.REACT_APP_CURRENT_BACKEND;
    }

    // Fallback to service
    return await configService.getCurrentBackend();
  } catch {
    const serviceName = import.meta.env.VITE_APP_SERVICE_NAME || '';
    if (serviceName.includes('spring')) return 'spring';
    if (serviceName.includes('nest')) return 'nest';
    if (serviceName.includes('django')) return 'django';
    return 'spring';
  }
};

// Environment-specific configurations
export const isDevelopment = import.meta.env.DEV;
export const isProduction = import.meta.env.PROD;
export const isKubernetes = import.meta.env.VITE_APP_ENV === 'dev' || import.meta.env.VITE_APP_ENV === 'staging' || import.meta.env.VITE_APP_ENV === 'prod';

export type { RuntimeConfig };
