name: Scale Kubernetes Clusters

on:
  schedule:
    - cron: '30 03 * * 1-6'  # 9:00 AM IST (Mon–Sat) - Scale up
    - cron: '30 15 * * 1-6'  # 9:00 PM IST (Mon–Sat) - Scale down

jobs:
  scale-all-clusters:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install doctl and jq
        run: |
          sudo snap install doctl
          sudo apt-get update && sudo apt-get install -y jq

      - name: Authenticate doctl
        env:
          DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DO_API_TOKEN }}
        run: doctl auth init -t "$DIGITALOCEAN_ACCESS_TOKEN"

      - name: Scale all clusters (scheduled)
        if: github.event_name == 'schedule'
        env:
          CLUSTER_IDS_JSON: ${{ secrets.CLUSTER_IDS_JSON }}
          CLUSTER_NODE_POOLS_JSON: ${{ secrets.CLUSTER_NODE_POOLS_JSON }}
          DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DO_API_TOKEN }}
        run: |
          set -e
          HOUR=$(date -u +"%H")
          MINUTE=$(date -u +"%M")
          DAY=$(date -u +"%u") # 1=Mon, ..., 7=Sun

          if [ "$HOUR" = "03" ] && [ "$MINUTE" = "30" ]; then
            # 9:00 AM IST (Mon-Sat) - Scale up
            if [ "$DAY" -eq 7 ]; then
              COUNT=0
              SCALE_TYPE="sunday_down"
              DO_SCALE="true"
            else
              COUNT=1
              SCALE_TYPE="up"
              DO_SCALE="true"
            fi
          elif [ "$HOUR" = "15" ] && [ "$MINUTE" = "30" ]; then
            # 9:00 PM IST (Mon-Sat) - Scale down
            COUNT=0
            SCALE_TYPE="down"
            DO_SCALE="true"
          else
            COUNT=0
            SCALE_TYPE="noop"
            DO_SCALE="false"
          fi

          if [ "$DO_SCALE" = "true" ]; then
            echo "SCALING_PERFORMED=true" >> $GITHUB_ENV
            echo "SCALE_TYPE_PERFORMED=$SCALE_TYPE" >> $GITHUB_ENV
            echo "NODE_COUNT_PERFORMED=$COUNT" >> $GITHUB_ENV

            CLUSTER_IDS=$(echo "$CLUSTER_IDS_JSON" | jq -r 'to_entries[] | @base64')
            for entry in $CLUSTER_IDS; do
              _jq() {
                echo ${entry} | base64 --decode | jq -r ${1}
              }
              CLUSTER_NAME=$(_jq '.key')
              CLUSTER_ID=$(_jq '.value')
              NODE_POOL_ID=$(echo "$CLUSTER_NODE_POOLS_JSON" | jq -r --arg name "$CLUSTER_NAME" '.[$name]')
              echo "Scaling $CLUSTER_NAME ($CLUSTER_ID), node pool $NODE_POOL_ID to $COUNT node(s)"

              if ! doctl kubernetes cluster node-pool update "$CLUSTER_ID" "$NODE_POOL_ID" --count "$COUNT"; then
                echo "Error: Failed to scale $CLUSTER_NAME"
                exit 1
              fi
            done
          else
            echo "SCALING_PERFORMED=false" >> $GITHUB_ENV
            echo "No scaling action required at this time."
          fi

      - name: Send scheduled scaling email notification
        if: github.event_name == 'schedule'
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 587
          username: <EMAIL>
          password: ${{ secrets.GMAIL_APP_PASSWORD }}
          subject: "DigitalOcean: Kubernetes Cluster Scaling with GitHub Actions"
          body: |
            Hello,

            This is an automated notification from GitHub Actions.

            Kubernetes Cluster Scaling Status:
            ------------------------------------------
            Workflow executed at ${{ github.run_started_at }}

            Status: ${{ env.SCALING_PERFORMED == 'true' && 'SCALING PERFORMED' || 'NO SCALING REQUIRED' }}

            Cluster Status:
            - dev-staging: ${{ env.SCALING_PERFORMED == 'true' && format('Scaled to {0} node(s)', env.NODE_COUNT_PERFORMED) || 'No change required' }}
            - semgrep-sonarqube: ${{ env.SCALING_PERFORMED == 'true' && format('Scaled to {0} node(s)', env.NODE_COUNT_PERFORMED) || 'No change required' }}
            - github-arc-argo: ${{ env.SCALING_PERFORMED == 'true' && format('Scaled to {0} node(s)', env.NODE_COUNT_PERFORMED) || 'No change required' }}
            - observability: ${{ env.SCALING_PERFORMED == 'true' && format('Scaled to {0} node(s)', env.NODE_COUNT_PERFORMED) || 'No change required' }}

            ${{ env.SCALING_PERFORMED == 'true' && format('Scale Operation: {0}', env.SCALE_TYPE_PERFORMED == 'up' && 'Scale UP' || env.SCALE_TYPE_PERFORMED == 'down' && 'Scale DOWN' || 'Sunday Scale DOWN') || 'Reason: Workflow runs outside of scheduled scaling times.' }}

            Scheduled Times:
            - Scale UP: 9:00 AM IST (Mon-Sat)
            - Scale DOWN: 9:00 PM IST (Mon-Sat)

            Event Type: Scheduled cron job
            Time: ${{ github.run_started_at }}

            ${{ env.SCALING_PERFORMED == 'true' && 'All clusters have been successfully scaled.' || 'This is a status notification to confirm the workflow is running correctly.' }}

            If you did not expect this workflow run, please review the workflow schedule in GitHub Actions.

            Best regards,
            GitHub Actions Cron Job
          to: <EMAIL>
          from: Donation Receipt <<EMAIL>>
          secure: false
