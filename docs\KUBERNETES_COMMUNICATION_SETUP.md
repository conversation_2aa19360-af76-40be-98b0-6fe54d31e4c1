# Kubernetes Communication Setup Guide

This guide provides step-by-step instructions to fix communication issues between the React frontend and Spring Boot backend in Kubernetes.

## 🎯 Problem Summary

**Current Issues:**
- React frontend (*************:3000) cannot communicate with Spring Boot backend (*************:8080)
- Frontend uses external IP instead of internal Kubernetes service communication
- Backend CORS configuration doesn't allow frontend's external IP
- Applications are in different namespaces (ai-react-frontend-dev vs ai-spring-backend-dev)
- Missing Kubernetes Service manifests

## 📋 Solution Overview

**Deliverables Created:**
1. ✅ Kubernetes environment configuration (.env.kubernetes.spring)
2. ✅ Service manifests for both applications
3. ✅ ConfigMap for backend CORS configuration
4. ✅ NetworkPolicy for cross-namespace communication
5. ✅ Updated Docker build process
6. ✅ Updated GitOps deployment configuration

## 🚀 Deployment Steps

### Step 1: Apply Namespace Labels
```bash
kubectl apply -f k8s/namespaces/namespace-labels.yaml
```

### Step 2: Deploy Services
```bash
# Deploy frontend service
kubectl apply -f k8s/services/ai-react-frontend-service.yaml

# Deploy backend service
kubectl apply -f k8s/services/ai-spring-backend-service.yaml
```

### Step 3: Apply Backend Configuration
```bash
# Apply CORS and environment configuration
kubectl apply -f k8s/configmaps/ai-spring-backend-cors-config.yaml
```

### Step 4: Configure Network Policies
```bash
# Enable cross-namespace communication
kubectl apply -f k8s/network-policies/cross-namespace-communication.yaml
```

### Step 5: Update Backend Deployment
Update your Spring Boot backend deployment to use the ConfigMaps:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-spring-backend
  namespace: ai-spring-backend-dev
spec:
  template:
    spec:
      containers:
      - name: ai-spring-backend
        image: your-backend-image:latest
        envFrom:
        - configMapRef:
            name: ai-spring-backend-env-config
        volumeMounts:
        - name: cors-config
          mountPath: /app/config
      volumes:
      - name: cors-config
        configMap:
          name: ai-spring-backend-cors-config
```

### Step 6: Deploy Frontend with Kubernetes Configuration
The CI/CD pipeline will automatically use `.env.kubernetes.spring` for deployments to main/deployment branches.

## 🔧 Configuration Details

### Environment Variables (Kubernetes)
```bash
VITE_APP_ENV=kubernetes
VITE_APP_API_URL=http://ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local:8080
VITE_APP_PORT=3000
VITE_APP_BACKEND_TYPE=kubernetes
VITE_APP_KUBERNETES_MODE=true
```

### CORS Configuration (Backend)
```properties
cors.allowed-origins=http://localhost:3000,http://*************:3000,http://ai-react-frontend-service.ai-react-frontend-dev.svc.cluster.local:3000
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
cors.allow-credentials=true
```

## 🧪 Testing and Verification

### Test 1: Service Discovery
```bash
# Check if services are created
kubectl get services -n ai-react-frontend-dev
kubectl get services -n ai-spring-backend-dev

# Test internal DNS resolution
kubectl run test-pod --image=busybox --rm -it -- nslookup ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local
```

### Test 2: Network Connectivity
```bash
# Test connectivity from frontend namespace to backend
kubectl run test-frontend --image=busybox --rm -it -n ai-react-frontend-dev -- wget -qO- http://ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local:8080/actuator/health
```

### Test 3: External Access
```bash
# Test external frontend access
curl http://*************:3000

# Test external backend access
curl http://*************:8080/actuator/health
```

### Test 4: CORS Verification
```bash
# Test CORS from frontend external IP
curl -H "Origin: http://*************:3000" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     http://*************:8080/api/v1/login
```

## 🔍 Troubleshooting

### Issue: Service Not Found
```bash
# Check service exists
kubectl get svc -n ai-spring-backend-dev

# Check endpoints
kubectl get endpoints -n ai-spring-backend-dev
```

### Issue: CORS Errors
```bash
# Check backend logs
kubectl logs -n ai-spring-backend-dev deployment/ai-spring-backend

# Verify ConfigMap is mounted
kubectl describe pod -n ai-spring-backend-dev -l app=ai-spring-backend
```

### Issue: Network Policy Blocking
```bash
# Check network policies
kubectl get networkpolicies -n ai-react-frontend-dev
kubectl get networkpolicies -n ai-spring-backend-dev

# Test without network policies (temporarily)
kubectl delete networkpolicy --all -n ai-react-frontend-dev
kubectl delete networkpolicy --all -n ai-spring-backend-dev
```

## 📊 Monitoring

### Check Application Logs
```bash
# Frontend logs
kubectl logs -n ai-react-frontend-dev deployment/ai-react-frontend

# Backend logs
kubectl logs -n ai-spring-backend-dev deployment/ai-spring-backend
```

### Monitor Network Traffic
```bash
# Check service endpoints
kubectl get endpoints -n ai-react-frontend-dev
kubectl get endpoints -n ai-spring-backend-dev
```

## 🎉 Success Criteria

✅ Frontend can make API calls to backend using internal service names
✅ External access to both applications works
✅ CORS allows requests from frontend external IP
✅ Cross-namespace communication is enabled
✅ No network policy blocking issues
✅ Both applications are accessible via LoadBalancer services
