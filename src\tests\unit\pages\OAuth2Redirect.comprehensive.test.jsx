import React from 'react';
import { render, waitFor, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import OAuth2Redirect from '../../../pages/OAuth2Redirect';

// Mock console.log to avoid test output noise
const originalConsoleLog = console.log;
beforeAll(() => {
  console.log = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
});

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Create a test theme
const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </ThemeProvider>
);

describe('OAuth2Redirect - Comprehensive Tests', () => {
  let originalLocation;
  let originalLocalStorage;
  let mockLocalStorage;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Store original location and localStorage
    originalLocation = window.location;
    originalLocalStorage = window.localStorage;
    
    // Mock localStorage
    mockLocalStorage = {
      setItem: jest.fn(),
      getItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
    };
    
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });

    // Mock window.location
    delete window.location;
    window.location = {
      search: '',
      href: '',
      pathname: '/',
      origin: 'http://localhost',
    };
  });

  afterEach(() => {
    // Restore original location and localStorage
    window.location = originalLocation;
    Object.defineProperty(window, 'localStorage', {
      value: originalLocalStorage,
      writable: true
    });
  });

  describe('Component Rendering', () => {
    it('renders loading spinner', () => {
      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
    });

    it('renders with correct styling', () => {
      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toBeInTheDocument();
    });
  });

  describe('localStorage setItem Function Patching', () => {
    it('patches localStorage.setItem when it is not a function', async () => {
      // Mock localStorage.setItem as undefined
      Object.defineProperty(window, 'localStorage', {
        value: {
          setItem: undefined,
          getItem: jest.fn(),
          removeItem: jest.fn(),
          clear: jest.fn(),
        },
        writable: true
      });

      // Set up window.setItemCalls to track calls
      window.setItemCalls = [];

      window.location.search = '?sessionToken=abc&refreshToken=def&userId=123&expiresAt=456&refreshExpiresAt=789';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(typeof window.localStorage.setItem).toBe('function');
      });

      // Test that the patched function works
      window.localStorage.setItem('test', 'value');
      expect(window.setItemCalls).toContainEqual(['test', 'value']);

      // Clean up
      delete window.setItemCalls;
    });

    it('does not patch localStorage.setItem when it is already a function', async () => {
      const originalSetItem = jest.fn();
      Object.defineProperty(window, 'localStorage', {
        value: {
          setItem: originalSetItem,
          getItem: jest.fn(),
          removeItem: jest.fn(),
          clear: jest.fn(),
        },
        writable: true
      });

      window.location.search = '?sessionToken=abc&refreshToken=def&userId=123&expiresAt=456&refreshExpiresAt=789';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(window.localStorage.setItem).toBe(originalSetItem);
      });
    });
  });

  describe('Successful OAuth2 Redirect', () => {
    it('stores tokens and redirects to dashboard when all parameters are present', async () => {
      window.location.search = '?sessionToken=abc123&refreshToken=def456&userId=user789&expiresAt=1234567890&refreshExpiresAt=9876543210';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('sessionToken', 'abc123');
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refreshToken', 'def456');
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('userId', 'user789');
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('expiresAt', '1234567890');
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refreshExpiresAt', '9876543210');
      });

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    it('stores tokens with different values', async () => {
      window.location.search = '?sessionToken=token1&refreshToken=token2&userId=user1&expiresAt=111&refreshExpiresAt=222';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('sessionToken', 'token1');
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refreshToken', 'token2');
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('userId', 'user1');
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('expiresAt', '111');
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refreshExpiresAt', '222');
      });

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });
  });

  describe('Failed OAuth2 Redirect', () => {
    it('redirects to signin when sessionToken is missing', async () => {
      window.location.search = '?refreshToken=def456&userId=user789&expiresAt=1234567890&refreshExpiresAt=9876543210';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
    });

    it('redirects to signin when refreshToken is missing', async () => {
      window.location.search = '?sessionToken=abc123&userId=user789&expiresAt=1234567890&refreshExpiresAt=9876543210';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
    });

    it('redirects to signin when userId is missing', async () => {
      window.location.search = '?sessionToken=abc123&refreshToken=def456&expiresAt=1234567890&refreshExpiresAt=9876543210';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
    });

    it('redirects to signin when expiresAt is missing', async () => {
      window.location.search = '?sessionToken=abc123&refreshToken=def456&userId=user789&refreshExpiresAt=9876543210';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
    });

    it('redirects to signin when refreshExpiresAt is missing', async () => {
      window.location.search = '?sessionToken=abc123&refreshToken=def456&userId=user789&expiresAt=1234567890';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
    });

    it('redirects to signin when no parameters are present', async () => {
      window.location.search = '';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
    });

    it('redirects to signin when parameters are empty strings', async () => {
      window.location.search = '?sessionToken=&refreshToken=&userId=&expiresAt=&refreshExpiresAt=';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });

      expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
    });
  });

  describe('Console Logging', () => {
    it('logs window.location.search', async () => {
      window.location.search = '?sessionToken=abc&refreshToken=def';

      render(
        <TestWrapper>
          <OAuth2Redirect />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(console.log).toHaveBeenCalledWith('COMPONENT window.location.search:', '?sessionToken=abc&refreshToken=def');
      });
    });
  });
});
