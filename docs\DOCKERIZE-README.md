# Dockerizing Your Vite/React App

This guide explains how to build, run, and manage your Vite/React application using Docker for multiple environments (spring, nest, django).

---

## 1. Prerequisites
- [Docker](https://www.docker.com/products/docker-desktop/) installed on your system
- This project cloned to your local machine

---

## 2. Environment Files

Create the following files in your project root:

**.env.spring**
```
VITE_API_URL=https://spring-backend.example.com
VITE_ENV=spring
```

**.env.nest**
```
VITE_API_URL=https://nest-backend.example.com
VITE_ENV=nest
```

**.env.django**
```
VITE_API_URL=https://django-backend.example.com
VITE_ENV=django
```

---

## 3. Dockerfile Overview

The Dockerfile uses build arguments to select the environment and build script:
- `ENV_FILE` (default: `.env.spring`)
- `BUILD_SCRIPT` (default: `build:spring`)

```
ARG ENV_FILE=.env.spring
COPY ${ENV_FILE} .env
ARG BUILD_SCRIPT=build:spring
RUN npm run $BUILD_SCRIPT
```

---

## 4. Build the Docker Image for Each Environment

**Spring (default):**
```
docker build -t ai-react-frontend-spring .
```

**Nest:**
```
docker build --build-arg ENV_FILE=.env.nest --build-arg BUILD_SCRIPT=build:nest -t ai-react-frontend-nest .
```

**Django:**
```
docker build --build-arg ENV_FILE=.env.django --build-arg BUILD_SCRIPT=build:django -t ai-react-frontend-django .
```

---

## 5. Run the Docker Container

**Spring:**
```
docker run -d -p 3000:80 --name ai-react-frontend-spring ai-react-frontend-spring
```

**Nest:**
```
docker run -d -p 3000:80 --name ai-react-frontend-nest ai-react-frontend-nest
```

**Django:**
```
docker run -d -p 3000:80 --name ai-react-frontend-django ai-react-frontend-django
```

---

## 6. Port Availability Check (Optional)

If port 3000 is in use, check available ports and use alternatives:

**Check if port 3000 is in use:**
```
netstat -ano | findstr :3000
```

**If port 3000 is busy, use alternative ports:**
```
# Spring on port 3001
docker run -d -p 3001:80 --name ai-react-frontend-spring ai-react-frontend-spring

# Nest on port 3002
docker run -d -p 3002:80 --name ai-react-frontend-nest ai-react-frontend-nest

# Django on port 3003
docker run -d -p 3003:80 --name ai-react-frontend-django ai-react-frontend-django
```

**Access your app at:**
- Spring: http://localhost:3000 (or 3001 if 3000 is busy)
- Nest: http://localhost:3000 (or 3002 if 3000 is busy)
- Django: http://localhost:3000 (or 3003 if 3000 is busy)

---

## 7. Stopping and Removing the Container

To stop a container:
```
docker stop <container-name>
# Example: docker stop ai-react-frontend-spring
```
To remove a container:
```
docker rm <container-name>
# Example: docker rm ai-react-frontend-spring
```

---

## 8. Pushing to Docker Hub (Optional)

Tag and push your image (replace `shashank1088` with your Docker Hub username):
```
docker tag ai-react-frontend-spring shashank1088/ai-react-frontend-spring:latest
docker push shashank1088/ai-react-frontend-spring:latest
```

---

## 9. Pull and Run on Another Machine

```
docker pull shashank1088/ai-react-frontend-spring:latest
docker run -d -p 3000:80 --name ai-react-frontend-spring shashank1088/ai-react-frontend-spring:latest
```

---

## 10. Troubleshooting
- Make sure the correct `.env` file is present for each build.
- Use the correct build arguments for each environment.
- Check Docker logs with `docker logs <container-name>` if something goes wrong.
- If port 3000 is busy, use alternative ports (3001, 3002, 3003, etc.).

--- 