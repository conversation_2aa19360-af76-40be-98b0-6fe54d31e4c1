export default {
  // Test environment
  testEnvironment: 'jsdom',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],

  // Mock import.meta for Jest
  globals: {
    'import.meta': {
      env: {
        VITE_APP_API_URL: 'http://localhost:8080',
        VITE_APP_ENV: 'test',
        VITE_APP_SERVICE_NAME: 'test-service',
        VITE_APP_BACKEND_NAMESPACE: 'test-namespace',
        VITE_APP_PORT: '3000',
        VITE_APP_GOOGLE_OAUTH_URL: 'http://oauth.test.com',
      }
    }
  },
  
  // Transform files
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
  },

  // File extensions to consider
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx'],

  // Test file patterns - prioritize tests/ folders and .test files
  testMatch: [
    '<rootDir>/src/tests/unit/**/*.(test|spec).(js|jsx|ts|tsx)',
    '<rootDir>/src/tests/integration/**/*.(test|spec).(js|jsx|ts|tsx)',
    '<rootDir>/src/tests/api/**/*.(test|spec).(js|jsx|ts|tsx)',
    '<rootDir>/src/**/__tests__/**/*.(test|spec).(js|jsx|ts|tsx)'
  ],
  roots: ['<rootDir>/src', '<rootDir>/src/tests'],

  // Exclude mock test files and unnecessary folders when running coverage
  testPathIgnorePatterns: [
    '/node_modules/',
    '/tests/unit/components/ui/',
    '/src/components/ui/',
    '\\.(simple)\\.(js|jsx|ts|tsx)$'
  ],

  // Coverage configuration - focus on tested components (exclude ui and test files)
  collectCoverageFrom: [
    'src/**/*.(js|jsx|ts|tsx)',
    '!src/**/*.d.ts',
    '!src/main.jsx',
    '!src/vite-env.d.ts',
    '!src/components/ui/**',
    '!src/**/__tests__/**',
    '!src/**/*.test.*',
    '!src/**/*.spec.*',
    '!src/tests/**',
    '!src/__mocks__/**',
    '!src/setupTests.js',
    '!src/services/configService.ts',
    '!src/config/env.ts',
  ],

  // Coverage thresholds - set to 80% (ignoring branches)
  coverageThreshold: {
    global: {
      functions: 80,
      lines: 80,
      statements: 80
    }
  },

  // Module name mapping for path aliases and static assets
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/src/__mocks__/fileMock.js'
  },
  
  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true,

  // Coverage reporting
  collectCoverage: false, // Only collect when explicitly requested
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  coverageDirectory: 'coverage',
};
