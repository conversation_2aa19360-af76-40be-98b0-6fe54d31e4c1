import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import SignUp from '@/pages/SignUp';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock AuthContext
const mockUseAuth = jest.fn();
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

// Mock AuthContainer
jest.mock('@/components/AuthContainer', () => {
  return function MockAuthContainer({ initialView }) {
    return <div data-testid="auth-container" data-initial-view={initialView}>AuthContainer</div>;
  };
});

// Mock <PERSON> components
jest.mock('@mui/material/styles', () => ({
  ThemeProvider: ({ children, theme }) => (
    <div data-testid="theme-provider" data-theme={JSON.stringify(theme)}>
      {children}
    </div>
  ),
  createTheme: (config) => config,
}));

jest.mock('@mui/material/CssBaseline', () => {
  return function MockCssBaseline() {
    return <div data-testid="css-baseline" />;
  };
});

jest.mock('@/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080',
}));

describe('SignUp Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  const renderWithRouter = (component) => {
    return render(
      <BrowserRouter>
        {component}
      </BrowserRouter>
    );
  };

  describe('Authentication Redirect', () => {
    test('should redirect to dashboard when user is authenticated', async () => {
      // Arrange
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      mockUseAuth.mockReturnValue({ user: mockUser });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    test('should not redirect when user is not authenticated', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      expect(mockNavigate).not.toHaveBeenCalled();
    });

    test('should not render form when user is authenticated', () => {
      // Arrange
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      mockUseAuth.mockReturnValue({ user: mockUser });

      // Act
      const { container } = renderWithRouter(<SignUp />);

      // Assert
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Component Rendering', () => {
    test('should render ThemeProvider with correct theme configuration', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      const themeProvider = screen.getByTestId('theme-provider');
      expect(themeProvider).toBeInTheDocument();
      
      const themeData = JSON.parse(themeProvider.getAttribute('data-theme'));
      expect(themeData.palette.primary.main).toBe('#1976d2');
      expect(themeData.palette.secondary.main).toBe('#dc004e');
      expect(themeData.typography.fontFamily).toBe('"Inter", "Roboto", "Helvetica", "Arial", sans-serif');
      expect(themeData.shape.borderRadius).toBe(8);
    });

    test('should render CssBaseline component', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      expect(screen.getByTestId('css-baseline')).toBeInTheDocument();
    });

    test('should render AuthContainer with signup initial view', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      const authContainer = screen.getByTestId('auth-container');
      expect(authContainer).toBeInTheDocument();
      expect(authContainer).toHaveAttribute('data-initial-view', 'signup');
    });

    test('should render complete component structure when not authenticated', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      expect(screen.getByTestId('theme-provider')).toBeInTheDocument();
      expect(screen.getByTestId('css-baseline')).toBeInTheDocument();
      expect(screen.getByTestId('auth-container')).toBeInTheDocument();
    });
  });

  describe('useAuth Integration', () => {
    test('should call useAuth hook', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      expect(mockUseAuth).toHaveBeenCalled();
    });

    test('should handle different user states', async () => {
      // Test with null user
      mockUseAuth.mockReturnValue({ user: null });
      const { rerender } = renderWithRouter(<SignUp />);
      expect(mockNavigate).not.toHaveBeenCalled();

      // Test with authenticated user
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      mockUseAuth.mockReturnValue({ user: mockUser });
      
      rerender(
        <BrowserRouter>
          <SignUp />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    test('should handle undefined user', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: undefined });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      expect(mockNavigate).not.toHaveBeenCalled();
      expect(screen.getByTestId('auth-container')).toBeInTheDocument();
    });
  });

  describe('Navigation Integration', () => {
    test('should use navigate hook correctly', async () => {
      // Arrange
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      mockUseAuth.mockReturnValue({ user: mockUser });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledTimes(1);
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    test('should handle navigation with different user objects', async () => {
      // Test with minimal user object
      const minimalUser = { id: '1' };
      mockUseAuth.mockReturnValue({ user: minimalUser });

      renderWithRouter(<SignUp />);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });

      // Reset mock
      mockNavigate.mockClear();

      // Test with complete user object
      const completeUser = {
        id: '2',
        email: '<EMAIL>',
        name: 'Complete User',
        profilePicture: 'https://example.com/pic.jpg'
      };
      mockUseAuth.mockReturnValue({ user: completeUser });

      renderWithRouter(<SignUp />);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });
  });

  describe('Component Lifecycle', () => {
    test('should handle component mount and unmount', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      const { unmount } = renderWithRouter(<SignUp />);
      
      // Assert - Component should render without errors
      expect(screen.getByTestId('auth-container')).toBeInTheDocument();
      
      // Act - Unmount component
      unmount();
      
      // Assert - Should not throw errors on unmount
      expect(() => unmount()).not.toThrow();
    });

    test('should re-run effect when user state changes', async () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });
      
      // Act
      const { rerender } = renderWithRouter(<SignUp />);
      expect(mockNavigate).not.toHaveBeenCalled();

      // Change user state
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      mockUseAuth.mockReturnValue({ user: mockUser });

      rerender(
        <BrowserRouter>
          <SignUp />
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle useAuth returning null context', () => {
      // Arrange
      mockUseAuth.mockReturnValue(null);

      // Act & Assert - Should not throw
      expect(() => renderWithRouter(<SignUp />)).not.toThrow();

      // Should render auth container when no user
      expect(screen.getByTestId('auth-container')).toBeInTheDocument();
    });

    test('should handle missing user property in auth context', () => {
      // Arrange
      mockUseAuth.mockReturnValue({});

      // Act & Assert - Should not throw
      expect(() => renderWithRouter(<SignUp />)).not.toThrow();

      // Should render auth container when no user
      expect(screen.getByTestId('auth-container')).toBeInTheDocument();
    });

    test('should handle useAuth returning undefined', () => {
      // Arrange
      mockUseAuth.mockReturnValue(undefined);

      // Act & Assert - Should not throw
      expect(() => renderWithRouter(<SignUp />)).not.toThrow();

      // Should render auth container when no user
      expect(screen.getByTestId('auth-container')).toBeInTheDocument();
    });
  });

  describe('Theme Configuration', () => {
    test('should use consistent theme with SignIn page', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      const themeProvider = screen.getByTestId('theme-provider');
      const themeData = JSON.parse(themeProvider.getAttribute('data-theme'));
      
      // Verify theme consistency
      expect(themeData.palette.primary.main).toBe('#1976d2');
      expect(themeData.palette.secondary.main).toBe('#dc004e');
      expect(themeData.typography.fontFamily).toBe('"Inter", "Roboto", "Helvetica", "Arial", sans-serif');
      expect(themeData.shape.borderRadius).toBe(8);
    });
  });

  describe('AuthContainer Integration', () => {
    test('should pass correct initialView prop to AuthContainer', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      const authContainer = screen.getByTestId('auth-container');
      expect(authContainer).toHaveAttribute('data-initial-view', 'signup');
    });

    test('should render AuthContainer when user is not authenticated', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderWithRouter(<SignUp />);

      // Assert
      expect(screen.getByTestId('auth-container')).toBeInTheDocument();
      expect(screen.getByText('AuthContainer')).toBeInTheDocument();
    });
  });
});
