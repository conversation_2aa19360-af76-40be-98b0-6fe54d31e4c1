import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ProtectedRoute from '@/components/ProtectedRoute';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  Navigate: ({ to, replace }) => {
    mockNavigate(to, replace);
    return <div data-testid="navigate" data-to={to} data-replace={replace} />;
  },
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, sx, component, ...props }) => <div data-testid="loading-box">{children}</div>,
  CircularProgress: ({ size }) => <div data-testid="loading-spinner" data-size={size} />,
  Typography: ({ children, variant, color }) => (
    <div data-testid="loading-text" data-variant={variant} data-color={color}>
      {children}
    </div>
  ),
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('ProtectedRoute', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockClear();
  });

  describe('Loading State', () => {
    test('should show loading spinner when localStorage access is delayed', async () => {
      // Arrange - Create a delayed localStorage mock
      let resolveGetItem;
      const delayedPromise = new Promise((resolve) => {
        resolveGetItem = resolve;
      });

      localStorageMock.getItem.mockImplementation((key) => {
        // Simulate async behavior by delaying the resolution
        setTimeout(() => resolveGetItem(null), 10);
        return null;
      });

      // Act
      const { container } = render(
        <BrowserRouter>
          <ProtectedRoute>
            <div data-testid="protected-content">Protected Content</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      // Assert - The component should eventually redirect after localStorage check
      await waitFor(() => {
        expect(screen.getByTestId('navigate')).toBeInTheDocument();
      });

      // Verify localStorage was called
      expect(localStorageMock.getItem).toHaveBeenCalledWith('userId');
      expect(localStorageMock.getItem).toHaveBeenCalledWith('sessionToken');
    });

    test('should handle component rendering lifecycle correctly', async () => {
      // Arrange
      localStorageMock.getItem.mockReturnValue(null);

      // Act
      render(
        <BrowserRouter>
          <ProtectedRoute>
            <div data-testid="protected-content">Content</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      // Assert - Should eventually redirect to signin
      await waitFor(() => {
        expect(screen.getByTestId('navigate')).toBeInTheDocument();
        expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/signin');
      });
    });
  });

  describe('Authentication Check', () => {
    test('should redirect to signin when no authentication tokens exist', async () => {
      // Arrange
      localStorageMock.getItem.mockReturnValue(null);
      
      // Act
      render(
        <BrowserRouter>
          <ProtectedRoute>
            <div data-testid="protected-content">Protected Content</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByTestId('navigate')).toBeInTheDocument();
        expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/signin');
        expect(screen.getByTestId('navigate')).toHaveAttribute('data-replace', 'true');
      });
      
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    test('should redirect to signin when only userId exists', async () => {
      // Arrange
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'userId') return 'user123';
        if (key === 'sessionToken') return null;
        return null;
      });
      
      // Act
      render(
        <BrowserRouter>
          <ProtectedRoute>
            <div data-testid="protected-content">Protected Content</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/signin');
      });
    });

    test('should redirect to signin when only sessionToken exists', async () => {
      // Arrange
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'userId') return null;
        if (key === 'sessionToken') return 'token123';
        return null;
      });
      
      // Act
      render(
        <BrowserRouter>
          <ProtectedRoute>
            <div data-testid="protected-content">Protected Content</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/signin');
      });
    });

    test('should render children when both userId and sessionToken exist', async () => {
      // Arrange
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'userId') return 'user123';
        if (key === 'sessionToken') return 'token123';
        return null;
      });
      
      // Act
      render(
        <BrowserRouter>
          <ProtectedRoute>
            <div data-testid="protected-content">Protected Content</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      });
      
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });
  });

  describe('localStorage Integration', () => {
    test('should call localStorage.getItem for userId and sessionToken', async () => {
      // Arrange
      localStorageMock.getItem.mockReturnValue('test-value');
      
      // Act
      render(
        <BrowserRouter>
          <ProtectedRoute>
            <div>Content</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      // Assert
      await waitFor(() => {
        expect(localStorageMock.getItem).toHaveBeenCalledWith('userId');
        expect(localStorageMock.getItem).toHaveBeenCalledWith('sessionToken');
      });
    });

    test('should handle localStorage errors gracefully', async () => {
      // Arrange
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      // Act & Assert - should not throw
      expect(() => {
        render(
          <BrowserRouter>
            <ProtectedRoute>
              <div data-testid="protected-content">Content</div>
            </ProtectedRoute>
          </BrowserRouter>
        );
      }).not.toThrow();
    });
  });

  describe('Component Lifecycle', () => {
    test('should check authentication on mount', () => {
      // Arrange
      localStorageMock.getItem.mockReturnValue(null);
      
      // Act
      render(
        <BrowserRouter>
          <ProtectedRoute>
            <div>Content</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      // Assert
      expect(localStorageMock.getItem).toHaveBeenCalled();
    });

    test('should not re-check authentication on re-render with same props', async () => {
      // Arrange
      localStorageMock.getItem.mockReturnValue('token');
      
      // Act
      const { rerender } = render(
        <BrowserRouter>
          <ProtectedRoute>
            <div>Content 1</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(localStorageMock.getItem).toHaveBeenCalled();
      });

      const initialCallCount = localStorageMock.getItem.mock.calls.length;

      rerender(
        <BrowserRouter>
          <ProtectedRoute>
            <div>Content 2</div>
          </ProtectedRoute>
        </BrowserRouter>
      );

      // Assert
      expect(localStorageMock.getItem.mock.calls.length).toBe(initialCallCount);
    });
  });
});
