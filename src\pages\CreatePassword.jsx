import React from 'react';
import { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import axios from "axios";
import { getApiBaseUrl } from "../config/env";
import {
  TOKEN_MESSAGES,
  USER_STATUS_MESSAGES,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
  BUTTON_TEXT,
  FORM_LABELS
} from "../constants/validationMessages";
import {
  FormContainer,
  FormHeader,
  AlertMessage,
  PasswordFormSection,
} from "../components/common";
import { usePasswordValidation, useApiCall, useDeviceInfo } from "../hooks";

const CreatePassword = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { setAuthenticatedUser } = useAuth();
  const token = searchParams.get("token");

  // Get API URL from environment
  const API_BASE_URL = getApiBaseUrl();

  // User data state
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");

  // Email verification state
  const [emailLoading, setEmailLoading] = useState(true);
  const [emailError, setEmailError] = useState("");
  const [isUserAlreadyActivated, setIsUserAlreadyActivated] = useState(false);

  // Password state
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [touched, setTouched] = useState({ password: false, confirmPassword: false });

  // Custom hooks
  const passwordValidation = usePasswordValidation(newPassword, confirmPassword, touched);
  const apiCall = useApiCall();
  const { getDeviceInfo } = useDeviceInfo();

  useEffect(() => {
    const fetchEmail = async () => {
      setEmailLoading(true);
      setEmailError("");
      setIsUserAlreadyActivated(false);
      try {
        const res = await axios.get(`${API_BASE_URL}/api/v1/verify-email?token=${token}`, {
          validateStatus: () => true, // Accept all status codes
        });

        if (res.status === 200 && res.data.email || res.status === 201 && res.data.email) {
          setEmail(res.data.email);
          setName(res.data.name || "");
          setMobileNumber(res.data.mobile_number || "");
        } else if (res.status === 409) {
          // User already activated - still allow password creation
          setEmail(res.data.email || "");
          setName(res.data.name || "");
          setMobileNumber(res.data.mobile_number || "");
          setIsUserAlreadyActivated(true);
        } else {
          setEmailError(res.data?.error || TOKEN_MESSAGES.INVALID_TOKEN);
        }
      } catch (error) {
        setEmailError(TOKEN_MESSAGES.INVALID_TOKEN);
      } finally {
        setEmailLoading(false);
      }
    };
    if (token) fetchEmail();
    else {
      setEmailError(TOKEN_MESSAGES.NO_TOKEN);
      setEmailLoading(false);
    }
  }, [token, API_BASE_URL]);

  // Check if form can be submitted
  const canCreatePassword = passwordValidation.isValid && !emailLoading && !emailError && (email || isUserAlreadyActivated);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setTouched({ password: true, confirmPassword: true });

    if (!passwordValidation.isValid || !email || emailError) {
      return;
    }

    const createUserCall = async () => {
      const deviceInfo = await getDeviceInfo();
      const payload = {
        email,
        password: newPassword,
        ...deviceInfo,
      };
      const response = await axios.post(`${API_BASE_URL}/api/v1/create-user`, payload, {
        headers: { "Content-Type": "application/json" },
        validateStatus: () => true,
      });

      if (response.status === 200 || response.status === 201) {
        // Store tokens in localStorage using camelCase response fields
        const { userId, sessionToken, refreshToken, expiresAt, refreshExpiresAt } = response.data;
        localStorage.setItem("userId", userId);
        localStorage.setItem("sessionToken", sessionToken);
        localStorage.setItem("refreshToken", refreshToken);
        localStorage.setItem("expiresAt", expiresAt);
        localStorage.setItem("refreshExpiresAt", refreshExpiresAt);

        // Create user object and set in AuthContext
        const userData = {
          id: userId,
          email: email,
          name: name || email.split('@')[0], // Use name from verification or fallback to email prefix
          profilePicture: undefined
        };

        setAuthenticatedUser(userData);

        // Redirect to dashboard immediately
        setTimeout(() => {
          navigate("/dashboard");
        }, 1000);

        return response.data;
      } else {
        const errorMessage = ERROR_MESSAGES.AUTH_CREATION_FAILED;
        setError(errorMessage);
      }
    };

    await apiCall.execute(createUserCall, {
      successMessage: SUCCESS_MESSAGES.AUTH_CREATED,
      errorMessage: ERROR_MESSAGES.AUTH_CREATION_FAILED,
    });
  };

  return (
    <FormContainer>
      <AlertMessage
        message={isUserAlreadyActivated ? USER_STATUS_MESSAGES.ALREADY_ACTIVATED : USER_STATUS_MESSAGES.ACCOUNT_ACTIVATED}
        severity={isUserAlreadyActivated ? "info" : "success"}
        show={email && !emailLoading && !emailError}
        sx={{ fontWeight: 600 }}
      />

      <FormHeader
        title={BUTTON_TEXT.CREATE_PASSWORD}
        subtitle="Please set a strong password for your account"
      />
      <AlertMessage
        message={TOKEN_MESSAGES.VERIFYING_EMAIL}
        severity="info"
        show={emailLoading}
      />

      <AlertMessage
        message={emailError}
        severity="error"
        show={!!emailError}
      />

      <AlertMessage
        message={SUCCESS_MESSAGES.AUTH_CREATED}
        severity="success"
        show={apiCall.success}
      />

      {!apiCall.success && (
        <PasswordFormSection
          onSubmit={handleSubmit}
          newPassword={newPassword}
          confirmPassword={confirmPassword}
          onPasswordChange={e => setNewPassword(e.target.value)}
          onConfirmPasswordChange={e => setConfirmPassword(e.target.value)}
          onPasswordBlur={() => setTouched(t => ({ ...t, password: true }))}
          onConfirmPasswordBlur={() => setTouched(t => ({ ...t, confirmPassword: true }))}
          passwordValidation={passwordValidation}
          passwordLabel={FORM_LABELS.NEW_AUTH_FIELD}
          confirmPasswordLabel={FORM_LABELS.CONFIRM_AUTH_FIELD}
          buttonText={BUTTON_TEXT.CREATE_PASSWORD}
          buttonDisabled={!canCreatePassword}
          buttonLoading={apiCall.loading}
          apiError={apiCall.error}
          disabled={emailLoading || !!emailError}
        />
      )}
    </FormContainer>
  );
};

export default CreatePassword; 