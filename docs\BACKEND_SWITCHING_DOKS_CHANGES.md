# 🔄 Backend Switching - DOKS Cluster Changes

## Overview
This document outlines the kubectl commands and cluster operations needed to implement dynamic backend switching in your DOKS cluster, integrated with the current environment structure.

## 🎯 Current Cluster Status
- **Cluster**: k8s-1-33-1-do-1-blr1-dev-staging (blr1)
- **Frontend**: ai-react-frontend-dev namespace (64.225.85.157:3000)
- **Backends**:
  - Spring Boot: ai-spring-backend-dev (*************:8080) ✅ Ready
  - Django: ai-django-backend-dev (*************:8000) ✅ Ready  
  - NestJS: ai-nest-backend-dev (**************:3000) ⚠️ Not Ready

## 📝 Current Environment Configuration

### Environment Structure
Based on your current setup:
```
.env.dev.spring     → Dev environment Spring (*************:8080)
.env.dev.nest       → Dev environment Nest
.env.dev.django     → Dev environment Django
.env.staging.*      → Staging environment configs
.env.prod.*         → Production environment configs
```

### Current Service Configuration
From `.env.dev.spring`:
```env
VITE_APP_ENV=dev
VITE_APP_API_URL=http://*************:8080
VITE_APP_SERVICE_NAME=ai-spring-backend-service
VITE_APP_BACKEND_NAMESPACE=ai-spring-backend-dev
```

---

## 🚀 ConfigMap Creation

### 1. Create Runtime Configuration ConfigMap

```bash
# Create ConfigMap with current backend configuration
kubectl create configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev \
  --from-literal=runtime-config.json='{
    "currentBackend": "spring",
    "backendUrl": "http://*************:8080",
    "environment": "dev",
    "serviceName": "ai-spring-backend-service",
    "namespace": "ai-spring-backend-dev",
    "apiVersion": "v1",
    "lastUpdated": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
    "supportedBackends": [
      {
        "name": "spring",
        "url": "http://*************:8080",
        "internalUrl": "http://ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local:8080",
        "serviceName": "ai-spring-backend-service",
        "namespace": "ai-spring-backend-dev",
        "status": "ready"
      },
      {
        "name": "django",
        "url": "http://*************:8000",
        "internalUrl": "http://ai-django-backend-service.ai-django-backend-dev.svc.cluster.local:8000",
        "serviceName": "ai-django-backend-service",
        "namespace": "ai-django-backend-dev",
        "status": "ready"
      },
      {
        "name": "nest",
        "url": "http://**************:3000",
        "internalUrl": "http://ai-nest-backend-service.ai-nest-backend-dev.svc.cluster.local:3000",
        "serviceName": "ai-nest-backend-service",
        "namespace": "ai-nest-backend-dev",
        "status": "pending"
      }
    ]
  }'
```

---

## 🔄 Backend Switching Commands

### Switch to Spring Boot (Current)
```bash
kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "runtime-config.json": "{\"currentBackend\":\"spring\",\"backendUrl\":\"http://*************:8080\",\"environment\":\"dev\",\"serviceName\":\"ai-spring-backend-service\",\"namespace\":\"ai-spring-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
  }
}'
```

### Switch to Django
```bash
kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "runtime-config.json": "{\"currentBackend\":\"django\",\"backendUrl\":\"http://*************:8000\",\"environment\":\"dev\",\"serviceName\":\"ai-django-backend-service\",\"namespace\":\"ai-django-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
  }
}'
```

### Switch to NestJS (when ready)
```bash
kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "runtime-config.json": "{\"currentBackend\":\"nest\",\"backendUrl\":\"http://**************:3000\",\"environment\":\"dev\",\"serviceName\":\"ai-nest-backend-service\",\"namespace\":\"ai-nest-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
  }
}'
```

---

## 📜 Automation Scripts

### Create Helper Scripts Directory
```bash
mkdir -p ~/k8s-scripts
```

### Spring Boot Switch Script
```bash
cat > ~/k8s-scripts/switch-to-spring.sh << 'EOF'
#!/bin/bash
echo "🔄 Switching to Spring Boot backend..."
kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "runtime-config.json": "{\"currentBackend\":\"spring\",\"backendUrl\":\"http://*************:8080\",\"environment\":\"dev\",\"serviceName\":\"ai-spring-backend-service\",\"namespace\":\"ai-spring-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
  }
}'
kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
echo "✅ Switched to Spring Boot backend (*************:8080)"
EOF
```

### Django Switch Script
```bash
cat > ~/k8s-scripts/switch-to-django.sh << 'EOF'
#!/bin/bash
echo "🔄 Switching to Django backend..."
kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "runtime-config.json": "{\"currentBackend\":\"django\",\"backendUrl\":\"http://*************:8000\",\"environment\":\"dev\",\"serviceName\":\"ai-django-backend-service\",\"namespace\":\"ai-django-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
  }
}'
kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
echo "✅ Switched to Django backend (*************:8000)"
EOF
```

### NestJS Switch Script
```bash
cat > ~/k8s-scripts/switch-to-nest.sh << 'EOF'
#!/bin/bash
echo "🔄 Switching to NestJS backend..."

# Check if NestJS backend is ready
if ! kubectl get service ai-nest-backend-service -n ai-nest-backend-dev >/dev/null 2>&1; then
  echo "❌ NestJS backend service not found. Please deploy NestJS backend first."
  exit 1
fi

kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
  "data": {
    "runtime-config.json": "{\"currentBackend\":\"nest\",\"backendUrl\":\"http://**************:3000\",\"environment\":\"dev\",\"serviceName\":\"ai-nest-backend-service\",\"namespace\":\"ai-nest-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
  }
}'
kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
echo "✅ Switched to NestJS backend (**************:3000)"
EOF
```

### Make Scripts Executable
```bash
chmod +x ~/k8s-scripts/*.sh
```

---

## 🔍 Verification Commands

### Check Current Configuration
```bash
# View current ConfigMap
kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o yaml

# Check current backend configuration
kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq .

# Verify frontend deployment status
kubectl get deployment ai-react-frontend -n ai-react-frontend-dev
kubectl get pods -n ai-react-frontend-dev -l app=ai-react-frontend
```

### Test Backend Connectivity
```bash
# Test Spring Boot backend
curl -f http://*************:8080/actuator/health || echo "Spring Boot not responding"

# Test Django backend  
curl -f http://*************:8000/health || echo "Django not responding"

# Test NestJS backend
curl -f http://**************:3000/health || echo "NestJS not responding"
```

### Monitor Frontend Logs
```bash
# Watch frontend logs for backend switching
kubectl logs -f deployment/ai-react-frontend -n ai-react-frontend-dev

# Check for configuration loading
kubectl logs deployment/ai-react-frontend -n ai-react-frontend-dev | grep -i config
```

---

## 🌍 Environment-Specific Configurations

### Development Environment (Current)
```json
{
  "currentBackend": "spring",
  "backendUrl": "http://*************:8080",
  "environment": "dev",
  "serviceName": "ai-spring-backend-service",
  "namespace": "ai-spring-backend-dev"
}
```

### Staging Environment
```json
{
  "currentBackend": "spring",
  "backendUrl": "http://ai-spring-backend-service.ai-spring-backend-staging.svc.cluster.local:8080",
  "environment": "staging",
  "serviceName": "ai-spring-backend-service",
  "namespace": "ai-spring-backend-staging"
}
```

### Production Environment
```json
{
  "currentBackend": "spring",
  "backendUrl": "http://ai-spring-backend-service.ai-spring-backend-prod.svc.cluster.local:8080",
  "environment": "prod",
  "serviceName": "ai-spring-backend-service",
  "namespace": "ai-spring-backend-prod"
}
```

---

## ✅ Testing Checklist

### Pre-Switch Verification
- [ ] Verify current backend is responding: `curl http://*************:8080/actuator/health`
- [ ] Check frontend is accessible: `curl http://64.225.85.157:3000`
- [ ] Confirm ConfigMap exists: `kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev`

### Post-Switch Verification
- [ ] Verify ConfigMap updated: `kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq .currentBackend`
- [ ] Check frontend pods restarted: `kubectl get pods -n ai-react-frontend-dev`
- [ ] Test API calls work: Access frontend and verify backend communication
- [ ] Monitor logs for errors: `kubectl logs deployment/ai-react-frontend -n ai-react-frontend-dev`

---

## 🚨 Troubleshooting

### Common Issues

1. **ConfigMap not found**
   ```bash
   kubectl create configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --from-literal=runtime-config.json='{}'
   ```

2. **Backend not responding**
   ```bash
   kubectl get services -n ai-spring-backend-dev
   kubectl get pods -n ai-spring-backend-dev
   ```

3. **Frontend not updating**
   ```bash
   kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
   kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
   ```

4. **CORS issues after switching**
   - Verify backend CORS configuration includes frontend URL
   - Check if backend allows the new frontend origin

---

## 📋 Quick Reference

### Current Backend URLs
- **Spring Boot**: `http://*************:8080`
- **Django**: `http://*************:8000`
- **NestJS**: `http://**************:3000` (pending)

### Frontend URL
- **Development**: `http://64.225.85.157:3000`

### Service Names
- **Spring**: `ai-spring-backend-service` in `ai-spring-backend-dev`
- **Django**: `ai-django-backend-service` in `ai-django-backend-dev`
- **NestJS**: `ai-nest-backend-service` in `ai-nest-backend-dev`



