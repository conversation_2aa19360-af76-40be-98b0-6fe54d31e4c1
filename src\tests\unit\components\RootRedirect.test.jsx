import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import RootRedirect from '@/components/RootRedirect';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock AuthContext
const mockUseAuth = jest.fn();
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  CircularProgress: ({ size, sx }) => (
    <div data-testid="loading-spinner" data-size={size} data-color={sx?.color}>
      Loading...
    </div>
  ),
  Box: ({ children, sx }) => (
    <div data-testid="loading-container" data-sx={JSON.stringify(sx)}>
      {children}
    </div>
  ),
}));

describe('RootRedirect', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  const renderComponent = () => {
    return render(
      <BrowserRouter>
        <RootRedirect />
      </BrowserRouter>
    );
  };

  describe('Loading State', () => {
    it('should render loading spinner initially', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderComponent();

      // Assert
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      expect(screen.getByTestId('loading-container')).toBeInTheDocument();
    });

    it('should render loading spinner with correct props', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderComponent();

      // Assert
      const spinner = screen.getByTestId('loading-spinner');
      expect(spinner).toHaveAttribute('data-size', '40');
      expect(spinner).toHaveAttribute('data-color', 'white');
    });

    it('should render loading container with correct styles', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderComponent();

      // Assert
      const container = screen.getByTestId('loading-container');
      const sx = JSON.parse(container.getAttribute('data-sx'));
      expect(sx).toEqual({
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      });
    });
  });

  describe('Navigation Logic', () => {
    it('should navigate to dashboard when user is authenticated', async () => {
      // Arrange
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'John Doe',
      };
      mockUseAuth.mockReturnValue({ user: mockUser });

      // Act
      renderComponent();
      jest.advanceTimersByTime(100);

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });

    it('should navigate to signin when user is not authenticated', async () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderComponent();
      jest.advanceTimersByTime(100);

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });

    it('should navigate to signin when user is undefined', async () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: undefined });

      // Act
      renderComponent();
      jest.advanceTimersByTime(100);

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });

    it('should use replace: true for navigation', async () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderComponent();
      jest.advanceTimersByTime(100);

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith(
          expect.any(String),
          { replace: true }
        );
      });
    });
  });

  describe('Timer Management', () => {
    it('should delay navigation by 100ms', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      renderComponent();

      // Assert - should not navigate immediately
      expect(mockNavigate).not.toHaveBeenCalled();

      // Fast-forward timer
      jest.advanceTimersByTime(99);
      expect(mockNavigate).not.toHaveBeenCalled();

      jest.advanceTimersByTime(1);
      expect(mockNavigate).toHaveBeenCalled();
    });

    it('should clear timer on unmount', () => {
      // Arrange
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      const { unmount } = renderComponent();
      unmount();

      // Assert
      expect(clearTimeoutSpy).toHaveBeenCalled();
      clearTimeoutSpy.mockRestore();
    });

    it('should handle user state changes', async () => {
      // Arrange
      const { rerender } = render(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Start with no user
      mockUseAuth.mockReturnValue({ user: null });
      rerender(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      // Change to authenticated user
      const mockUser = { id: 'user123', email: '<EMAIL>' };
      mockUseAuth.mockReturnValue({ user: mockUser });
      rerender(
        <BrowserRouter>
          <RootRedirect />
        </BrowserRouter>
      );

      jest.advanceTimersByTime(100);

      // Assert
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle useAuth returning null', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act & Assert - should not throw
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle useAuth returning undefined', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: undefined });

      // Act & Assert - should not throw
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle missing user property', async () => {
      // Arrange
      mockUseAuth.mockReturnValue({});

      // Act
      renderComponent();
      jest.advanceTimersByTime(100);

      // Assert - should treat as unauthenticated
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin', { replace: true });
      });
    });
  });

  describe('Component Lifecycle', () => {
    it('should render without errors', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act & Assert
      expect(() => renderComponent()).not.toThrow();
    });

    it('should handle multiple re-renders', () => {
      // Arrange
      mockUseAuth.mockReturnValue({ user: null });

      // Act
      const { rerender } = renderComponent();
      
      for (let i = 0; i < 3; i++) {
        rerender(
          <BrowserRouter>
            <RootRedirect />
          </BrowserRouter>
        );
      }

      // Assert - should not throw
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });
  });
});
