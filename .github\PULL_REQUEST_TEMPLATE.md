## The PR includes:
- [ ] Provide summary of the frontend changes

## Description
Briefly describe the purpose of this change and any relevant context.

## Technical Details
Provide a summary of what was changed in the codebase, such as affected components, modules, or hooks.

## Checklist

- [ ] Application builds successfully using npm or yarn
- [ ] All unit and integration tests pass
- [ ] Linting and formatting checks pass
- [ ] No console logs or commented-out code left in the branch
- [ ] Components are responsive and tested on multiple screen sizes
- [ ] Accessibility best practices are followed (where applicable)
- [ ] No hardcoded values or sensitive information in the codebase
- [ ] SonarQube quality gate passed
- [ ] Semgrep scan completed with no high or critical issues

## Related Issue / Ticket
Add a link or reference to the relevant issue, task, or ticket.

## Additional Notes
Include any setup instructions, testing notes, screenshots, or reviewer guidance.
