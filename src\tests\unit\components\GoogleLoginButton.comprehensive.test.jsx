import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import GoogleLoginButton from '../../../components/GoogleLoginButton';
import { AuthProvider } from '../../../contexts/AuthContext';

// Mock environment variables
const mockEnv = {
  VITE_APP_GOOGLE_OAUTH_URL: 'https://accounts.google.com/oauth/authorize?client_id=test'
};

// Mock import.meta.env
const mockImportMeta = {
  env: mockEnv
};

// Mock the import.meta object
global.importMeta = mockImportMeta;

// Mock the component's import.meta usage
jest.mock('../../../components/GoogleLoginButton', () => {
  const React = require('react');
  const { Button, Box } = require('@mui/material');

  return function GoogleLoginButton() {
    const googleOAuthUrl = mockEnv.VITE_APP_GOOGLE_OAUTH_URL;
    const { isLoading } = require('../../../contexts/AuthContext').useAuth();

    const handleGoogleLogin = () => {
      window.location.href = googleOAuthUrl;
    };

    return React.createElement(Button, {
      fullWidth: true,
      variant: "outlined",
      onClick: handleGoogleLogin,
      disabled: isLoading,
      size: "medium",
      sx: {
        py: 1.25,
        border: '1.5px solid #e0e0e0',
        color: '#757575',
        fontWeight: 600,
        textTransform: 'none',
        fontSize: '0.9rem',
        borderRadius: 1.5,
        '&:hover': {
          border: '1.5px solid #1976d2',
          backgroundColor: '#f5f5f5',
        },
        '&:disabled': {
          opacity: 0.6,
        },
      }
    }, React.createElement(Box, {
      sx: { display: 'flex', alignItems: 'center', gap: 1.5 }
    }, React.createElement('svg', {
      width: "18",
      height: "18",
      viewBox: "0 0 24 24"
    },
      React.createElement('path', {
        fill: "#4285F4",
        d: "M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
      }),
      React.createElement('path', {
        fill: "#34A853",
        d: "M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
      }),
      React.createElement('path', {
        fill: "#FBBC05",
        d: "M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
      }),
      React.createElement('path', {
        fill: "#EA4335",
        d: "M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
      })
    ), 'Continue with Google'));
  };
});

// Mock window.location
const mockLocation = {
  href: '',
  assign: jest.fn(),
  replace: jest.fn(),
  reload: jest.fn()
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true
});

// Create a test theme
const theme = createTheme();

// Test wrapper component
const TestWrapper = ({ children, authContextValue = {} }) => {
  const defaultAuthValue = {
    isLoading: false,
    user: null,
    login: jest.fn(),
    logout: jest.fn(),
    ...authContextValue
  };

  return (
    <ThemeProvider theme={theme}>
      <AuthProvider value={defaultAuthValue}>
        {children}
      </AuthProvider>
    </ThemeProvider>
  );
};

describe('GoogleLoginButton - Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocation.href = '';
    import.meta.env.VITE_APP_GOOGLE_OAUTH_URL = mockEnv.VITE_APP_GOOGLE_OAUTH_URL;
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders the Google login button', () => {
      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('Continue with Google');
    });

    it('renders with correct button attributes', () => {
      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('type', 'button');
      expect(button).not.toBeDisabled();
    });

    it('renders Google SVG icon', () => {
      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const svgElement = screen.getByRole('button').querySelector('svg');
      expect(svgElement).toBeInTheDocument();
      expect(svgElement).toHaveAttribute('width', '18');
      expect(svgElement).toHaveAttribute('height', '18');
    });

    it('renders all Google icon paths', () => {
      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const svgElement = screen.getByRole('button').querySelector('svg');
      const paths = svgElement.querySelectorAll('path');
      expect(paths).toHaveLength(4);
      
      // Check for specific Google brand colors
      expect(paths[0]).toHaveAttribute('fill', '#4285F4'); // Blue
      expect(paths[1]).toHaveAttribute('fill', '#34A853'); // Green
      expect(paths[2]).toHaveAttribute('fill', '#FBBC05'); // Yellow
      expect(paths[3]).toHaveAttribute('fill', '#EA4335'); // Red
    });
  });

  describe('Click Functionality', () => {
    it('redirects to Google OAuth URL when clicked', () => {
      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe(mockEnv.VITE_APP_GOOGLE_OAUTH_URL);
    });

    it('handles click with different OAuth URL', () => {
      const customUrl = 'https://custom-oauth.example.com';
      import.meta.env.VITE_APP_GOOGLE_OAUTH_URL = customUrl;

      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe(customUrl);
    });

    it('handles multiple clicks', () => {
      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      expect(mockLocation.href).toBe(mockEnv.VITE_APP_GOOGLE_OAUTH_URL);
    });
  });

  describe('Loading State', () => {
    it('disables button when loading', () => {
      render(
        <TestWrapper authContextValue={{ isLoading: true }}>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('enables button when not loading', () => {
      render(
        <TestWrapper authContextValue={{ isLoading: false }}>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      expect(button).not.toBeDisabled();
    });

    it('does not redirect when disabled and clicked', () => {
      render(
        <TestWrapper authContextValue={{ isLoading: true }}>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe('');
    });
  });

  describe('Environment Variables', () => {
    it('handles missing OAuth URL', () => {
      import.meta.env.VITE_APP_GOOGLE_OAUTH_URL = undefined;

      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe('undefined');
    });

    it('handles empty OAuth URL', () => {
      import.meta.env.VITE_APP_GOOGLE_OAUTH_URL = '';

      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe('');
    });
  });

  describe('Accessibility', () => {
    it('has accessible button role', () => {
      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('has descriptive text content', () => {
      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      expect(button).toHaveTextContent('Continue with Google');
    });

    it('supports keyboard navigation', () => {
      render(
        <TestWrapper>
          <GoogleLoginButton />
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      button.focus();
      expect(button).toHaveFocus();
    });
  });

  describe('Error Handling', () => {
    it('handles window.location assignment errors gracefully', () => {
      const originalLocation = window.location;
      
      // Mock location to throw error
      Object.defineProperty(window, 'location', {
        value: {
          set href(url) {
            throw new Error('Navigation blocked');
          }
        },
        writable: true
      });

      expect(() => {
        render(
          <TestWrapper>
            <GoogleLoginButton />
          </TestWrapper>
        );
        
        const button = screen.getByRole('button');
        fireEvent.click(button);
      }).not.toThrow();

      // Restore original location
      Object.defineProperty(window, 'location', {
        value: originalLocation,
        writable: true
      });
    });
  });
});
