import { renderHook, act } from '@testing-library/react';
import { useToast, toast, reducer } from '@/hooks/use-toast';

// Mock the toast UI component
jest.mock('@/components/ui/toast', () => ({
  ToastActionElement: {},
  ToastProps: {},
}));

// Mock setTimeout and clearTimeout
jest.useFakeTimers();

describe('useToast', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    // Reset the module state between tests
    jest.resetModules();
  });

  afterEach(() => {
    act(() => {
      jest.runOnlyPendingTimers();
    });
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  describe('Initial State', () => {
    test('should return empty toasts array initially', () => {
      // Act
      const { result } = renderHook(() => useToast());

      // Assert
      expect(result.current.toasts).toEqual([]);
    });

    test('should provide toast function', () => {
      // Act
      const { result } = renderHook(() => useToast());

      // Assert
      expect(typeof result.current.toast).toBe('function');
    });

    test('should provide dismiss function', () => {
      // Act
      const { result } = renderHook(() => useToast());

      // Assert
      expect(typeof result.current.dismiss).toBe('function');
    });
  });

  describe('Toast Creation', () => {
    test('should create a toast with basic properties', () => {
      // Arrange
      const { result } = renderHook(() => useToast());

      // Act
      act(() => {
        result.current.toast({
          title: 'Test Toast',
          description: 'Test Description',
        });
      });

      // Assert
      expect(result.current.toasts).toHaveLength(1);
      expect(result.current.toasts[0]).toMatchObject({
        title: 'Test Toast',
        description: 'Test Description',
        open: true,
      });
      expect(result.current.toasts[0].id).toBeDefined();
    });

    test('should create toast with unique IDs', () => {
      // Arrange
      const { result } = renderHook(() => useToast());

      // Act
      act(() => {
        result.current.toast({ title: 'Toast 1' });
        result.current.toast({ title: 'Toast 2' });
      });

      // Assert
      expect(result.current.toasts).toHaveLength(1); // TOAST_LIMIT is 1
      expect(result.current.toasts[0].title).toBe('Toast 2'); // Latest toast replaces previous
    });

    test('should respect TOAST_LIMIT', () => {
      // Arrange
      const { result } = renderHook(() => useToast());

      // Act
      act(() => {
        result.current.toast({ title: 'Toast 1' });
        result.current.toast({ title: 'Toast 2' });
        result.current.toast({ title: 'Toast 3' });
      });

      // Assert
      expect(result.current.toasts).toHaveLength(1);
      expect(result.current.toasts[0].title).toBe('Toast 3'); // Latest toast replaces previous
    });

    test('should create toast with onOpenChange handler', () => {
      // Arrange
      const { result } = renderHook(() => useToast());

      // Act
      act(() => {
        result.current.toast({
          title: 'Test Toast',
        });
      });

      // Assert
      expect(result.current.toasts[0].onOpenChange).toBeDefined();
      expect(typeof result.current.toasts[0].onOpenChange).toBe('function');
    });
  });

  describe('Toast Dismissal', () => {
    test('should dismiss specific toast by ID', () => {
      // Arrange
      const { result } = renderHook(() => useToast());
      let toastId: string;

      act(() => {
        const toastResult = result.current.toast({ title: 'Test Toast' });
        toastId = toastResult.id;
      });

      // Act
      act(() => {
        result.current.dismiss(toastId);
      });

      // Assert
      expect(result.current.toasts[0].open).toBe(false);
    });

    test('should dismiss all toasts when no ID provided', () => {
      // Arrange
      const { result } = renderHook(() => useToast());

      act(() => {
        result.current.toast({ title: 'Toast 1' });
      });

      // Act
      act(() => {
        result.current.dismiss();
      });

      // Assert
      expect(result.current.toasts[0].open).toBe(false);
    });

    test('should call onOpenChange when dismissing via onOpenChange', () => {
      // Arrange
      const { result } = renderHook(() => useToast());

      act(() => {
        result.current.toast({ title: 'Test Toast' });
      });

      const toast = result.current.toasts[0];

      // Act
      act(() => {
        toast.onOpenChange?.(false);
      });

      // Assert
      expect(result.current.toasts[0].open).toBe(false);
    });

    test('should not dismiss when onOpenChange called with true', () => {
      // Arrange
      const { result } = renderHook(() => useToast());

      act(() => {
        result.current.toast({ title: 'Test Toast' });
      });

      const toast = result.current.toasts[0];

      // Act
      act(() => {
        toast.onOpenChange?.(true);
      });

      // Assert
      expect(result.current.toasts[0].open).toBe(true);
    });
  });

  describe('Toast Removal', () => {
    test('should remove toast after delay', () => {
      // Arrange
      const { result } = renderHook(() => useToast());
      let toastId: string;

      act(() => {
        const toastResult = result.current.toast({ title: 'Test Toast' });
        toastId = toastResult.id;
      });

      // Act - Dismiss the toast
      act(() => {
        result.current.dismiss(toastId);
      });

      // Fast-forward time to trigger removal
      act(() => {
        jest.advanceTimersByTime(1000000); // TOAST_REMOVE_DELAY
      });

      // Assert
      expect(result.current.toasts).toHaveLength(0);
    });

    test('should not add duplicate timeouts for same toast', () => {
      // Arrange
      const { result } = renderHook(() => useToast());
      let toastId: string;

      act(() => {
        const toastResult = result.current.toast({ title: 'Test Toast' });
        toastId = toastResult.id;
      });

      // Act - Dismiss multiple times
      act(() => {
        result.current.dismiss(toastId);
        result.current.dismiss(toastId);
        result.current.dismiss(toastId);
      });

      // Fast-forward time
      act(() => {
        jest.advanceTimersByTime(1000000);
      });

      // Assert - Should only be removed once
      expect(result.current.toasts).toHaveLength(0);
    });
  });

  describe('Toast Updates', () => {
    test('should update toast properties', () => {
      // Arrange
      const { result } = renderHook(() => useToast());
      let toastResult: any;

      act(() => {
        toastResult = result.current.toast({ title: 'Original Title' });
      });

      // Act
      act(() => {
        toastResult.update({
          title: 'Updated Title',
          description: 'New Description',
        });
      });

      // Assert
      expect(result.current.toasts[0].title).toBe('Updated Title');
      expect(result.current.toasts[0].description).toBe('New Description');
    });

    test('should return update function from toast creation', () => {
      // Arrange
      const { result } = renderHook(() => useToast());

      // Act
      let toastResult: any;
      act(() => {
        toastResult = result.current.toast({ title: 'Test Toast' });
      });

      // Assert
      expect(typeof toastResult.update).toBe('function');
    });

    test('should return dismiss function from toast creation', () => {
      // Arrange
      const { result } = renderHook(() => useToast());

      // Act
      let toastResult: any;
      act(() => {
        toastResult = result.current.toast({ title: 'Test Toast' });
      });

      // Assert
      expect(typeof toastResult.dismiss).toBe('function');
    });
  });

  describe('Standalone Toast Function', () => {
    test('should work independently of useToast hook', () => {
      // Act
      let toastResult: any;
      act(() => {
        toastResult = toast({ title: 'Standalone Toast' });
      });

      // Assert
      expect(toastResult.id).toBeDefined();
      expect(typeof toastResult.dismiss).toBe('function');
      expect(typeof toastResult.update).toBe('function');
    });

    test('should generate unique IDs for standalone toasts', () => {
      // Act
      let toast1: any, toast2: any;
      act(() => {
        toast1 = toast({ title: 'Toast 1' });
        toast2 = toast({ title: 'Toast 2' });
      });

      // Assert
      expect(toast1.id).not.toBe(toast2.id);
    });
  });

  describe('Hook State Management', () => {
    test('should sync state across multiple hook instances', () => {
      // Arrange
      const { result: result1 } = renderHook(() => useToast());
      const { result: result2 } = renderHook(() => useToast());

      // Act
      act(() => {
        result1.current.toast({ title: 'Test Toast' });
      });

      // Assert - Both hooks should see the same state
      expect(result1.current.toasts).toHaveLength(1);
      expect(result2.current.toasts).toHaveLength(1);
      expect(result1.current.toasts[0].title).toBe('Test Toast');
      expect(result2.current.toasts[0].title).toBe('Test Toast');
    });

    test('should cleanup listeners on unmount', () => {
      // Arrange
      const { result, unmount } = renderHook(() => useToast());

      // Act
      unmount();

      // Assert - Should not throw errors when unmounted
      expect(() => unmount()).not.toThrow();
    });
  });
});

describe('Toast Reducer', () => {
  const initialState = { toasts: [] };
  const mockToast = {
    id: '1',
    title: 'Test Toast',
    description: 'Test Description',
    open: true,
  };

  describe('ADD_TOAST', () => {
    test('should add toast to empty state', () => {
      // Act
      const result = reducer(initialState, {
        type: 'ADD_TOAST',
        toast: mockToast,
      });

      // Assert
      expect(result.toasts).toHaveLength(1);
      expect(result.toasts[0]).toEqual(mockToast);
    });

    test('should add toast to beginning of array', () => {
      // Arrange
      const stateWithToast = {
        toasts: [{ ...mockToast, id: '2', title: 'Existing Toast' }],
      };

      // Act
      const result = reducer(stateWithToast, {
        type: 'ADD_TOAST',
        toast: mockToast,
      });

      // Assert
      expect(result.toasts).toHaveLength(1); // TOAST_LIMIT is 1
      expect(result.toasts[0]).toEqual(mockToast);
    });

    test('should respect TOAST_LIMIT', () => {
      // Arrange
      const stateWithMaxToasts = {
        toasts: [
          { ...mockToast, id: '2', title: 'Toast 2' },
        ],
      };

      // Act
      const result = reducer(stateWithMaxToasts, {
        type: 'ADD_TOAST',
        toast: mockToast,
      });

      // Assert
      expect(result.toasts).toHaveLength(1);
      expect(result.toasts[0]).toEqual(mockToast);
    });
  });

  describe('UPDATE_TOAST', () => {
    test('should update existing toast', () => {
      // Arrange
      const stateWithToast = { toasts: [mockToast] };

      // Act
      const result = reducer(stateWithToast, {
        type: 'UPDATE_TOAST',
        toast: { id: '1', title: 'Updated Title' },
      });

      // Assert
      expect(result.toasts[0]).toEqual({
        ...mockToast,
        title: 'Updated Title',
      });
    });

    test('should not update non-existing toast', () => {
      // Arrange
      const stateWithToast = { toasts: [mockToast] };

      // Act
      const result = reducer(stateWithToast, {
        type: 'UPDATE_TOAST',
        toast: { id: 'non-existing', title: 'Updated Title' },
      });

      // Assert
      expect(result.toasts[0]).toEqual(mockToast);
    });

    test('should update multiple properties', () => {
      // Arrange
      const stateWithToast = { toasts: [mockToast] };

      // Act
      const result = reducer(stateWithToast, {
        type: 'UPDATE_TOAST',
        toast: {
          id: '1',
          title: 'New Title',
          description: 'New Description',
        },
      });

      // Assert
      expect(result.toasts[0]).toEqual({
        ...mockToast,
        title: 'New Title',
        description: 'New Description',
      });
    });
  });

  describe('DISMISS_TOAST', () => {
    test('should dismiss specific toast', () => {
      // Arrange
      const stateWithToast = { toasts: [mockToast] };

      // Act
      const result = reducer(stateWithToast, {
        type: 'DISMISS_TOAST',
        toastId: '1',
      });

      // Assert
      expect(result.toasts[0].open).toBe(false);
    });

    test('should dismiss all toasts when no ID provided', () => {
      // Arrange
      const stateWithToasts = {
        toasts: [
          { ...mockToast, id: '1' },
          { ...mockToast, id: '2' },
        ],
      };

      // Act
      const result = reducer(stateWithToasts, {
        type: 'DISMISS_TOAST',
      });

      // Assert
      expect(result.toasts[0].open).toBe(false);
      expect(result.toasts[1].open).toBe(false);
    });

    test('should not affect non-matching toasts', () => {
      // Arrange
      const stateWithToasts = {
        toasts: [
          { ...mockToast, id: '1' },
          { ...mockToast, id: '2' },
        ],
      };

      // Act
      const result = reducer(stateWithToasts, {
        type: 'DISMISS_TOAST',
        toastId: '1',
      });

      // Assert
      expect(result.toasts[0].open).toBe(false);
      expect(result.toasts[1].open).toBe(true);
    });
  });

  describe('REMOVE_TOAST', () => {
    test('should remove specific toast', () => {
      // Arrange
      const stateWithToasts = {
        toasts: [
          { ...mockToast, id: '1' },
          { ...mockToast, id: '2' },
        ],
      };

      // Act
      const result = reducer(stateWithToasts, {
        type: 'REMOVE_TOAST',
        toastId: '1',
      });

      // Assert
      expect(result.toasts).toHaveLength(1);
      expect(result.toasts[0].id).toBe('2');
    });

    test('should remove all toasts when no ID provided', () => {
      // Arrange
      const stateWithToasts = {
        toasts: [
          { ...mockToast, id: '1' },
          { ...mockToast, id: '2' },
        ],
      };

      // Act
      const result = reducer(stateWithToasts, {
        type: 'REMOVE_TOAST',
      });

      // Assert
      expect(result.toasts).toHaveLength(0);
    });

    test('should handle removing non-existing toast', () => {
      // Arrange
      const stateWithToast = { toasts: [mockToast] };

      // Act
      const result = reducer(stateWithToast, {
        type: 'REMOVE_TOAST',
        toastId: 'non-existing',
      });

      // Assert
      expect(result.toasts).toHaveLength(1);
      expect(result.toasts[0]).toEqual(mockToast);
    });
  });
});
