import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import AuthContainer from '../../../components/AuthContainer';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock AuthContext
const mockUseAuth = {
  user: null,
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth,
}));

// Mock components
jest.mock('../../../components/LoginForm', () => {
  return function LoginForm({ onSwitchToSignUp, onSwitchToForgotPassword }) {
    return (
      <div data-testid="login-form">
        <h2>Login Form</h2>
        <button onClick={onSwitchToSignUp} data-testid="switch-to-signup">
          Switch to Sign Up
        </button>
        <button onClick={onSwitchToForgotPassword} data-testid="switch-to-forgot">
          Switch to Forgot Password
        </button>
      </div>
    );
  };
});

jest.mock('../../../components/SignUpForm', () => {
  return function SignUpForm({ onSwitchToLogin }) {
    return (
      <div data-testid="signup-form">
        <h2>Sign Up Form</h2>
        <button onClick={onSwitchToLogin} data-testid="switch-to-login">
          Switch to Login
        </button>
      </div>
    );
  };
});

jest.mock('../../../components/ForgotPasswordForm', () => {
  return function ForgotPasswordForm({ onSwitchToLogin }) {
    return (
      <div data-testid="forgot-password-form">
        <h2>Forgot Password Form</h2>
        <button onClick={onSwitchToLogin} data-testid="switch-to-login">
          Switch to Login
        </button>
      </div>
    );
  };
});

jest.mock('../../../components/UserDashboard', () => {
  return function UserDashboard() {
    return (
      <div data-testid="user-dashboard">
        <h2>User Dashboard</h2>
      </div>
    );
  };
});

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, sx }) => <div data-testid="box">{children}</div>,
  Container: ({ children, maxWidth }) => <div data-testid="container">{children}</div>,
  Paper: ({ children, elevation, sx }) => <div data-testid="paper">{children}</div>,
}));

// Wrapper component for router
const AuthContainerWrapper = ({ initialView }) => (
  <BrowserRouter>
    <AuthContainer initialView={initialView} />
  </BrowserRouter>
);

describe('AuthContainer - Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.user = null;
  });

  describe('Initial Render', () => {
    it('renders with default login view', () => {
      render(<AuthContainerWrapper />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('paper')).toBeInTheDocument();
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
      expect(screen.getByText('Login Form')).toBeInTheDocument();
    });

    it('renders with login view when explicitly set', () => {
      render(<AuthContainerWrapper initialView="login" />);
      
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
      expect(screen.getByText('Login Form')).toBeInTheDocument();
      expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('forgot-password-form')).not.toBeInTheDocument();
    });

    it('renders with signup view when set', () => {
      render(<AuthContainerWrapper initialView="signup" />);
      
      expect(screen.getByTestId('signup-form')).toBeInTheDocument();
      expect(screen.getByText('Sign Up Form')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('forgot-password-form')).not.toBeInTheDocument();
    });

    it('renders with forgot-password view when set', () => {
      render(<AuthContainerWrapper initialView="forgot-password" />);
      
      expect(screen.getByTestId('forgot-password-form')).toBeInTheDocument();
      expect(screen.getByText('Forgot Password Form')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
    });

    it('renders nothing for invalid view', () => {
      // Suppress PropTypes warning for this edge case test
      const originalConsoleError = console.error;
      console.error = jest.fn();

      render(<AuthContainerWrapper initialView="invalid" />);

      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('paper')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('forgot-password-form')).not.toBeInTheDocument();

      console.error = originalConsoleError;
    });
  });

  describe('User Authentication State', () => {
    it('renders UserDashboard when user is authenticated', () => {
      mockUseAuth.user = {
        id: '123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      render(<AuthContainerWrapper />);
      
      expect(screen.getByTestId('user-dashboard')).toBeInTheDocument();
      expect(screen.getByText('User Dashboard')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('box')).not.toBeInTheDocument();
    });

    it('renders auth forms when user is not authenticated', () => {
      mockUseAuth.user = null;

      render(<AuthContainerWrapper />);
      
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
      expect(screen.queryByTestId('user-dashboard')).not.toBeInTheDocument();
    });

    it('renders UserDashboard regardless of initialView when user is authenticated', () => {
      mockUseAuth.user = {
        id: '456',
        email: '<EMAIL>',
        name: 'Another User'
      };

      render(<AuthContainerWrapper initialView="signup" />);
      
      expect(screen.getByTestId('user-dashboard')).toBeInTheDocument();
      expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
    });
  });

  describe('Navigation Functionality', () => {
    it('navigates to signup when switch button is clicked from login', () => {
      render(<AuthContainerWrapper initialView="login" />);
      
      const switchButton = screen.getByTestId('switch-to-signup');
      fireEvent.click(switchButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('/signup');
    });

    it('navigates to forgot password when switch button is clicked from login', () => {
      render(<AuthContainerWrapper initialView="login" />);
      
      const switchButton = screen.getByTestId('switch-to-forgot');
      fireEvent.click(switchButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('/forgot-password');
    });

    it('navigates to signin when switch button is clicked from signup', () => {
      render(<AuthContainerWrapper initialView="signup" />);
      
      const switchButton = screen.getByTestId('switch-to-login');
      fireEvent.click(switchButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('/signin');
    });

    it('navigates to signin when switch button is clicked from forgot password', () => {
      render(<AuthContainerWrapper initialView="forgot-password" />);
      
      const switchButton = screen.getByTestId('switch-to-login');
      fireEvent.click(switchButton);
      
      expect(mockNavigate).toHaveBeenCalledWith('/signin');
    });

    it('handles multiple navigation calls', () => {
      render(<AuthContainerWrapper initialView="login" />);
      
      const signupButton = screen.getByTestId('switch-to-signup');
      const forgotButton = screen.getByTestId('switch-to-forgot');
      
      fireEvent.click(signupButton);
      fireEvent.click(forgotButton);
      
      expect(mockNavigate).toHaveBeenCalledTimes(2);
      expect(mockNavigate).toHaveBeenNthCalledWith(1, '/signup');
      expect(mockNavigate).toHaveBeenNthCalledWith(2, '/forgot-password');
    });
  });

  describe('Props and PropTypes', () => {
    it('accepts valid initialView props', () => {
      // Test all valid props
      const validViews = ['login', 'signup', 'forgot-password'];
      
      validViews.forEach(view => {
        const { unmount } = render(<AuthContainerWrapper initialView={view} />);
        expect(screen.getByTestId('box')).toBeInTheDocument();
        unmount();
      });
    });

    it('uses default props when no initialView is provided', () => {
      render(<AuthContainerWrapper />);
      
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
    });

    it('handles undefined initialView gracefully', () => {
      render(<AuthContainerWrapper initialView={undefined} />);
      
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
    });

    it('handles null initialView gracefully', () => {
      render(<AuthContainerWrapper initialView={null} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('paper')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('renders correct component hierarchy', () => {
      render(<AuthContainerWrapper />);
      
      const box = screen.getByTestId('box');
      const container = screen.getByTestId('container');
      const paper = screen.getByTestId('paper');
      const loginForm = screen.getByTestId('login-form');
      
      expect(box).toContainElement(container);
      expect(container).toContainElement(paper);
      expect(paper).toContainElement(loginForm);
    });

    it('renders all required MUI components', () => {
      render(<AuthContainerWrapper />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('paper')).toBeInTheDocument();
    });
  });

  describe('View Switching Logic', () => {
    it('switches views correctly based on initialView changes', () => {
      const { rerender } = render(<AuthContainerWrapper initialView="login" />);
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
      
      rerender(<AuthContainerWrapper initialView="signup" />);
      expect(screen.getByTestId('signup-form')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
      
      rerender(<AuthContainerWrapper initialView="forgot-password" />);
      expect(screen.getByTestId('forgot-password-form')).toBeInTheDocument();
      expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
    });

    it('maintains view state when initialView does not change', () => {
      const { rerender } = render(<AuthContainerWrapper initialView="signup" />);
      expect(screen.getByTestId('signup-form')).toBeInTheDocument();
      
      rerender(<AuthContainerWrapper initialView="signup" />);
      expect(screen.getByTestId('signup-form')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    // Suppress PropTypes warnings for edge case tests
    let originalConsoleError;

    beforeEach(() => {
      originalConsoleError = console.error;
      console.error = jest.fn(); // Suppress all console.error for edge case tests
    });

    afterEach(() => {
      console.error = originalConsoleError;
    });

    it('handles empty string initialView', () => {
      render(<AuthContainerWrapper initialView="" />);

      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('paper')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
    });

    it('handles boolean initialView', () => {
      render(<AuthContainerWrapper initialView={true} />);

      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('paper')).toBeInTheDocument();
    });

    it('handles number initialView', () => {
      render(<AuthContainerWrapper initialView={123} />);

      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('paper')).toBeInTheDocument();
    });

    it('handles object initialView', () => {
      render(<AuthContainerWrapper initialView={{}} />);

      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('paper')).toBeInTheDocument();
    });
  });

  describe('Integration with Auth Context', () => {
    it('responds to auth context changes', () => {
      const { rerender } = render(<AuthContainerWrapper />);
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
      
      // Simulate user login
      mockUseAuth.user = { id: '123', email: '<EMAIL>' };
      rerender(<AuthContainerWrapper />);
      
      expect(screen.getByTestId('user-dashboard')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
    });

    it('handles user logout', () => {
      mockUseAuth.user = { id: '123', email: '<EMAIL>' };
      const { rerender } = render(<AuthContainerWrapper />);
      expect(screen.getByTestId('user-dashboard')).toBeInTheDocument();
      
      // Simulate user logout
      mockUseAuth.user = null;
      rerender(<AuthContainerWrapper />);
      
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
      expect(screen.queryByTestId('user-dashboard')).not.toBeInTheDocument();
    });
  });
});
