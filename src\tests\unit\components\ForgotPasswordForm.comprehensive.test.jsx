import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import axios from 'axios';
import ForgotPasswordForm from '../../../components/ForgotPasswordForm';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Mock environment config
jest.mock('../../../config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:3000',
}));

// Mock validation messages
jest.mock('../../../constants/validationMessages', () => ({
  EMAIL_MESSAGES: {
    REQUIRED: 'Email is required',
    INVALID: 'Please enter a valid email address'
  },
  SUCCESS_MESSAGES: {
    EMAIL_SENT: 'Reset link sent successfully'
  },
  ERROR_MESSAGES: {
    NETWORK_ERROR: 'Network error occurred'
  },
  BUTTON_TEXT: {
    SEND_RESET_LINK: 'Send Reset Link',
    TRY_AGAIN: 'Try Again'
  },
  FORM_LABELS: {
    EMAIL: 'Email Address'
  },
  REGEX_PATTERNS: {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  }
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, component, onSubmit, sx }) => 
    component === 'form' ? 
      <form onSubmit={onSubmit} data-testid="forgot-password-form">{children}</form> : 
      <div data-testid="box">{children}</div>,
  TextField: ({ label, value, onChange, error, helperText, disabled, type, fullWidth, size, InputProps, sx }) => (
    <div>
      <label>{label}</label>
      <input
        data-testid="email-input"
        type={type}
        value={value}
        onChange={onChange}
        disabled={disabled}
      />
      {error && <span data-testid="email-error">{helperText}</span>}
    </div>
  ),
  Button: ({ children, onClick, disabled, type, variant, fullWidth, size, sx }) => (
    <button 
      type={type}
      onClick={onClick}
      disabled={disabled}
      data-testid={variant === 'outlined' ? 'try-again-button' : 'submit-button'}
    >
      {children}
    </button>
  ),
  Typography: ({ children, variant, component, sx, gutterBottom }) => (
    <div data-testid={`typography-${variant}`}>{children}</div>
  ),
  Link: ({ children, onClick, component, type, sx }) => 
    <button type={type} onClick={onClick} data-testid="back-to-login-link">{children}</button>,
  InputAdornment: ({ children, position }) => <span data-testid="input-adornment">{children}</span>,
  CircularProgress: ({ size, color }) => <div data-testid="loading-spinner">Loading...</div>,
  Alert: ({ children, severity, sx }) => (
    <div data-testid={`alert-${severity}`}>{children}</div>
  ),
}));

// Mock lucide-react
jest.mock('lucide-react', () => ({
  Mail: ({ size, color }) => <span data-testid="mail-icon">📧</span>,
}));

describe('ForgotPasswordForm - Comprehensive Tests', () => {
  const mockOnSwitchToLogin = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock IP address API
    mockedAxios.get.mockResolvedValue({ data: { ip: '***********' } });
  });

  describe('Initial Render', () => {
    it('renders form with all elements', () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      expect(screen.getByTestId('typography-h5')).toHaveTextContent('Forgot Password');
      expect(screen.getByText(/Enter your email address/)).toBeInTheDocument();
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
      expect(screen.getByTestId('submit-button')).toBeInTheDocument();
      expect(screen.getByTestId('back-to-login-link')).toBeInTheDocument();
      // Form elements are properly rendered
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
    });

    it('renders with correct labels and text', () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      expect(screen.getByText('Email Address')).toBeInTheDocument();
      expect(screen.getByText('Send Reset Link')).toBeInTheDocument();
      expect(screen.getByText('Back to sign in')).toBeInTheDocument();
      expect(screen.getByText('Remember your password?')).toBeInTheDocument();
    });

    it('has email input with correct type', () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      const emailInput = screen.getByTestId('email-input');
      expect(emailInput).toHaveAttribute('type', 'email');
    });
  });

  describe('Form Validation', () => {
    it('shows error when email is empty', async () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);
      
      await waitFor(() => {
        expect(screen.getByTestId('email-error')).toHaveTextContent('Email is required');
      });
    });

    it('shows error for invalid email format', async () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      
      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);
      
      await waitFor(() => {
        expect(screen.getByTestId('email-error')).toHaveTextContent('Please enter a valid email address');
      });
    });

    it('clears error when user starts typing valid email', () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      // First trigger an error
      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);
      
      // Then start typing
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      // Error should be cleared
      expect(screen.queryByTestId('email-error')).not.toBeInTheDocument();
    });

    it('accepts valid email format', async () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);
      
      // Should not show email error
      await waitFor(() => {
        expect(screen.queryByTestId('email-error')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Submission', () => {
    it('handles successful form submission', async () => {
      mockedAxios.post.mockResolvedValueOnce({ status: 200, data: { success: true } });

      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);
      
      // Should show loading state
      await waitFor(() => {
        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      });
      
      // Should eventually show success page
      await waitFor(() => {
        expect(screen.getByText('Check Your Email')).toBeInTheDocument();
        expect(screen.getByText('Password reset request submitted')).toBeInTheDocument();
        expect(screen.getByTestId('alert-info')).toBeInTheDocument();
      }, { timeout: 2000 });
    });

    it('handles API error gracefully (shows same success message)', async () => {
      mockedAxios.post.mockRejectedValueOnce(new Error('Network error'));

      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);
      
      // Should still show success page for security reasons
      await waitFor(() => {
        expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      }, { timeout: 2000 });
    });

    it('handles IP address fetch failure', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('IP fetch failed'));
      mockedAxios.post.mockResolvedValueOnce({ status: 200, data: { success: true } });

      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);
      
      await waitFor(() => {
        expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      }, { timeout: 2000 });

      // Should still make API call with empty IP
      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/forgot-password',
        expect.objectContaining({
          email: '<EMAIL>',
          ipAddress: '',
          deviceDetails: expect.any(String)
        }),
        expect.any(Object)
      );
    });

    it('makes API call with correct payload', async () => {
      mockedAxios.post.mockResolvedValueOnce({ status: 200, data: { success: true } });

      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);
      
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          'http://localhost:3000/api/v1/forgot-password',
          {
            email: '<EMAIL>',
            ipAddress: '***********',
            deviceDetails: navigator.userAgent
          },
          {
            headers: { 'Content-Type': 'application/json' },
            validateStatus: expect.any(Function)
          }
        );
      });
    });

    it('disables form during submission', async () => {
      // Mock a delayed response
      mockedAxios.post.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ status: 200, data: { success: true } }), 100))
      );

      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      
      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      
      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);
      
      // Should disable input and button during loading
      expect(emailInput).toBeDisabled();
      expect(screen.getByTestId('submit-button')).toBeDisabled();
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });
  });

  describe('Success State', () => {
    beforeEach(async () => {
      mockedAxios.post.mockResolvedValueOnce({ status: 200, data: { success: true } });

      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);

      // Wait for success state
      await waitFor(() => {
        expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      }, { timeout: 2000 });
    });

    it('shows success message and instructions', () => {
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
      expect(screen.getByText('Password reset request submitted')).toBeInTheDocument();
      expect(screen.getByTestId('alert-info')).toBeInTheDocument();
      expect(screen.getByText(/If the email exists in our system/)).toBeInTheDocument();
      expect(screen.getByText(/Please check your inbox and spam folder/)).toBeInTheDocument();
    });

    it('shows try again button', () => {
      expect(screen.getByText('Didn\'t receive the email?')).toBeInTheDocument();
      expect(screen.getByTestId('try-again-button')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('shows back to login link in success state', () => {
      expect(screen.getByText('Remember your password?')).toBeInTheDocument();
      expect(screen.getByTestId('back-to-login-link')).toBeInTheDocument();
      expect(screen.getByText('Back to sign in')).toBeInTheDocument();
    });

    it('handles try again button click', () => {
      const tryAgainButton = screen.getByTestId('try-again-button');
      fireEvent.click(tryAgainButton);

      // Should return to form state
      expect(screen.getByTestId('forgot-password-form')).toBeInTheDocument();
      expect(screen.getByText('Forgot Password')).toBeInTheDocument();
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
    });

    it('calls onSwitchToLogin from success state', () => {
      const backToLoginLink = screen.getByTestId('back-to-login-link');
      fireEvent.click(backToLoginLink);

      expect(mockOnSwitchToLogin).toHaveBeenCalled();
    });
  });

  describe('Navigation', () => {
    it('calls onSwitchToLogin when back to login link is clicked from form', () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const backToLoginLink = screen.getByTestId('back-to-login-link');
      fireEvent.click(backToLoginLink);

      expect(mockOnSwitchToLogin).toHaveBeenCalled();
    });

    it('handles multiple clicks on back to login link', () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const backToLoginLink = screen.getByTestId('back-to-login-link');
      fireEvent.click(backToLoginLink);
      fireEvent.click(backToLoginLink);

      expect(mockOnSwitchToLogin).toHaveBeenCalledTimes(2);
    });
  });

  describe('Input Handling', () => {
    it('updates email value on input change', () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(emailInput.value).toBe('<EMAIL>');
    });

    it('handles empty input after typing', () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(emailInput, { target: { value: '' } });

      expect(emailInput.value).toBe('');
    });

    it('handles special characters in email', () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(emailInput.value).toBe('<EMAIL>');
    });
  });

  describe('Device Details', () => {
    it('includes device details in API call', async () => {
      mockedAxios.post.mockResolvedValueOnce({ status: 200, data: { success: true } });

      // Mock navigator.userAgent
      const originalUserAgent = navigator.userAgent;
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Test Browser)',
        configurable: true
      });

      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);

      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          'http://localhost:3000/api/v1/forgot-password',
          expect.objectContaining({
            deviceDetails: 'Mozilla/5.0 (Test Browser)'
          }),
          expect.any(Object)
        );
      });

      // Restore original userAgent
      Object.defineProperty(navigator, 'userAgent', {
        value: originalUserAgent,
        configurable: true
      });
    });
  });

  describe('Edge Cases', () => {
    it('handles form submission without email change', async () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);

      await waitFor(() => {
        expect(screen.getByTestId('email-error')).toHaveTextContent('Email is required');
      });
    });

    it('handles rapid form submissions', async () => {
      mockedAxios.post.mockResolvedValueOnce({ status: 200, data: { success: true } });

      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);

      // Wait for loading state to be set
      await waitFor(() => {
        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      });

      // Second submission should be ignored due to loading state
      fireEvent.submit(form);

      // Should only make one API call
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledTimes(1);
      }, { timeout: 2000 });
    });

    it('preserves email value during error state', async () => {
      render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });

      const form = screen.getByTestId('forgot-password-form');
      fireEvent.submit(form);

      await waitFor(() => {
        expect(screen.getByTestId('email-error')).toBeInTheDocument();
      });

      // Email value should be preserved
      expect(emailInput.value).toBe('invalid-email');
    });
  });
});
