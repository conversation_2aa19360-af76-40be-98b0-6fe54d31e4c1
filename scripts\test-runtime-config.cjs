#!/usr/bin/env node

/**
 * Test script to validate runtime configuration implementation
 * This script tests the new window._env_ approach
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Runtime Configuration Implementation...\n');

// Test 1: Check if env-config.js exists
const envConfigPath = path.join(__dirname, '..', 'public', 'env-config.js');
console.log('1. Checking if env-config.js exists...');
if (fs.existsSync(envConfigPath)) {
  console.log('   ✅ env-config.js found at:', envConfigPath);
} else {
  console.log('   ❌ env-config.js not found');
  process.exit(1);
}

// Test 2: Check if env-config.js has valid content
console.log('\n2. Validating env-config.js content...');
try {
  const envConfigContent = fs.readFileSync(envConfigPath, 'utf8');
  
  // Check for required properties
  const requiredProps = [
    'REACT_APP_BACKEND_URL',
    'REACT_APP_CURRENT_BACKEND',
    'REACT_APP_ENVIRONMENT'
  ];
  
  let allPropsFound = true;
  requiredProps.forEach(prop => {
    if (envConfigContent.includes(prop)) {
      console.log(`   ✅ ${prop} found`);
    } else {
      console.log(`   ❌ ${prop} missing`);
      allPropsFound = false;
    }
  });
  
  if (allPropsFound) {
    console.log('   ✅ All required properties found');
  } else {
    console.log('   ❌ Some required properties missing');
    process.exit(1);
  }
} catch (error) {
  console.log('   ❌ Error reading env-config.js:', error.message);
  process.exit(1);
}

// Test 3: Check if index.html includes the script tag
console.log('\n3. Checking if index.html includes env-config.js script...');
const indexHtmlPath = path.join(__dirname, '..', 'index.html');
try {
  const indexHtmlContent = fs.readFileSync(indexHtmlPath, 'utf8');
  
  if (indexHtmlContent.includes('env-config.js')) {
    console.log('   ✅ Script tag found in index.html');
  } else {
    console.log('   ❌ Script tag not found in index.html');
    process.exit(1);
  }
} catch (error) {
  console.log('   ❌ Error reading index.html:', error.message);
  process.exit(1);
}

// Test 4: Check TypeScript declarations
console.log('\n4. Checking TypeScript declarations...');
const viteEnvPath = path.join(__dirname, '..', 'src', 'vite-env.d.ts');
try {
  const viteEnvContent = fs.readFileSync(viteEnvPath, 'utf8');
  
  if (viteEnvContent.includes('RuntimeEnvironment') && viteEnvContent.includes('_env_: RuntimeEnvironment')) {
    console.log('   ✅ TypeScript declarations found');
  } else {
    console.log('   ❌ TypeScript declarations missing or incomplete');
    process.exit(1);
  }
} catch (error) {
  console.log('   ❌ Error reading vite-env.d.ts:', error.message);
  process.exit(1);
}

// Test 5: Check if configService.ts has been updated
console.log('\n5. Checking configService.ts updates...');
const configServicePath = path.join(__dirname, '..', 'src', 'services', 'configService.ts');
try {
  const configServiceContent = fs.readFileSync(configServicePath, 'utf8');
  
  if (configServiceContent.includes('window._env_')) {
    console.log('   ✅ configService.ts updated to use window._env_');
  } else {
    console.log('   ❌ configService.ts not updated');
    process.exit(1);
  }
} catch (error) {
  console.log('   ❌ Error reading configService.ts:', error.message);
  process.exit(1);
}

// Test 6: Check if env.ts has been updated
console.log('\n6. Checking env.ts updates...');
const envTsPath = path.join(__dirname, '..', 'src', 'config', 'env.ts');
try {
  const envTsContent = fs.readFileSync(envTsPath, 'utf8');
  
  if (envTsContent.includes('window._env_')) {
    console.log('   ✅ env.ts updated to use window._env_');
  } else {
    console.log('   ❌ env.ts not updated');
    process.exit(1);
  }
} catch (error) {
  console.log('   ❌ Error reading env.ts:', error.message);
  process.exit(1);
}

// Test 7: Check if build includes env-config.js
console.log('\n7. Checking if build includes env-config.js...');
const distEnvConfigPath = path.join(__dirname, '..', 'dist', 'env-config.js');
if (fs.existsSync(distEnvConfigPath)) {
  console.log('   ✅ env-config.js found in dist folder');
} else {
  console.log('   ⚠️  env-config.js not found in dist folder (run build first)');
}

console.log('\n🎉 Runtime Configuration Implementation Test Complete!');
console.log('\n📋 Summary:');
console.log('   ✅ env-config.js created with proper configuration');
console.log('   ✅ index.html updated to load runtime config');
console.log('   ✅ TypeScript declarations added');
console.log('   ✅ configService.ts updated to use window._env_');
console.log('   ✅ env.ts updated to support runtime config');

console.log('\n🚀 Next Steps:');
console.log('   1. Test the application in browser');
console.log('   2. Verify configuration loads correctly');
console.log('   3. Test backend switching functionality');
console.log('   4. Deploy to Kubernetes with ConfigMap');

console.log('\n🔧 To test in browser:');
console.log('   1. Open browser dev tools');
console.log('   2. Check console for runtime config logs');
console.log('   3. Verify window._env_ is available');
console.log('   4. Test API calls use correct backend URL');
