import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import ResetPassword from '@/pages/ResetPassword';
import axios from 'axios';

// Mock GoogleLoginButton to avoid import.meta.env issues
jest.mock('@/components/GoogleLoginButton', () => ({
  __esModule: true,
  default: () => <div data-testid="google-login-button" />
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
let mockSearchParams = new URLSearchParams();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useSearchParams: () => [mockSearchParams],
}));

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock environment configuration
jest.mock('@/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080',
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, component, ...props }) => {
    if (component === 'form') {
      return <form data-testid="box" {...props}>{children}</form>;
    }
    return <div data-testid="box" {...props}>{children}</div>;
  },
  Paper: ({ children, elevation, sx, ...props }) => (
    <div data-testid="paper" data-elevation={elevation} {...props}>{children}</div>
  ),
  Typography: ({ children, variant, component, gutterBottom, sx, ...props }) => (
    <div data-testid="typography" data-variant={variant} data-component={component} {...props}>
      {children}
    </div>
  ),
  TextField: ({ label, value, onChange, fullWidth, margin, variant, type, error, helperText, InputProps, placeholder, ...props }) => (
    <div data-testid="text-field-container">
      <input
        data-testid="text-field"
        data-label={label}
        value={value}
        onChange={onChange}
        data-fullwidth={fullWidth}
        data-margin={margin}
        data-variant={variant}
        type={type}
        data-error={error}
        placeholder={placeholder || label}
        {...props}
      />
      {helperText && <div data-testid="helper-text">{helperText}</div>}
      {InputProps?.endAdornment && <div data-testid="input-adornment">{InputProps.endAdornment}</div>}
    </div>
  ),
  Button: ({ children, variant, color, fullWidth, sx, onClick, disabled, type, ...props }) => (
    <button
      data-testid="button"
      data-variant={variant}
      data-color={color}
      data-fullwidth={fullWidth}
      onClick={onClick}
      disabled={disabled}
      type={type}
      {...props}
    >
      {children}
    </button>
  ),
  Alert: ({ children, severity, message, sx, ...props }) => (
    <div data-testid="alert" data-severity={severity} {...props}>
      {children}
    </div>
  ),
  InputAdornment: ({ children, position, ...props }) => (
    <div data-testid="input-adornment" data-position={position} {...props}>{children}</div>
  ),
  IconButton: ({ children, onClick, ...props }) => (
    <button data-testid="icon-button" onClick={onClick} {...props}>{children}</button>
  ),
  CircularProgress: ({ size, ...props }) => (
    <div data-testid="circular-progress" data-size={size} {...props} />
  ),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Eye: () => <div data-testid="eye-icon" />,
  EyeOff: () => <div data-testid="eye-off-icon" />,
  Lock: () => <div data-testid="lock-icon" />,
}));

describe('ResetPassword Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
    mockSearchParams = new URLSearchParams();
    mockedAxios.get.mockClear();
    mockedAxios.post.mockClear();
  });

  const renderWithRouter = (component) => {
    return render(
      <BrowserRouter>
        {component}
      </BrowserRouter>
    );
  };

  describe('Token Validation', () => {
    test('should show error when no token is provided', async () => {
      // Arrange - No token in search params
      mockSearchParams.delete('token');

      // Act
      renderWithRouter(<ResetPassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('No reset token provided.')).toBeInTheDocument();
      });
    });

    test('should show form when token is present', () => {
      // Arrange
      mockSearchParams.set('token', 'valid-token-123');

      // Act
      renderWithRouter(<ResetPassword />);

      // Assert
      expect(screen.getAllByText('Reset Password')[0]).toBeInTheDocument();
      expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Confirm Password')).toBeInTheDocument();
    });

    test('should show form when valid token is provided', async () => {
      // Arrange
      mockSearchParams.set('token', 'valid-token-123');

      // Act
      renderWithRouter(<ResetPassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getAllByText('Reset Password')[0]).toBeInTheDocument();
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Confirm Password')).toBeInTheDocument();
      });
    });
  });

  describe('Form Rendering', () => {
    beforeEach(() => {
      mockSearchParams.set('token', 'valid-token-123');
    });

    test('should render password reset form', async () => {
      // Act
      renderWithRouter(<ResetPassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getAllByText('Reset Password')[0]).toBeInTheDocument();
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Confirm Password')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Reset Password' })).toBeInTheDocument();
      });
    });

    test('should render form elements', async () => {
      // Act
      renderWithRouter(<ResetPassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Confirm Password')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Reset Password' })).toBeInTheDocument();
      });
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      mockSearchParams.set('token', 'valid-token-123');
    });

    test('should show password validation errors', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<ResetPassword />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByPlaceholderText('New Password');
      await user.type(passwordField, 'weak');
      await user.tab(); // Trigger blur to show validation

      // Assert
      await waitFor(() => {
        expect(screen.getByText('At least 8 characters')).toBeInTheDocument();
      });
    });

    test('should show password mismatch error', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<ResetPassword />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByPlaceholderText('New Password');
      const confirmField = screen.getByPlaceholderText('Confirm Password');
      
      await user.type(passwordField, 'ValidPass123!');
      await user.type(confirmField, 'DifferentPass123!');
      await user.tab(); // Trigger validation

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Passwords do not match')).toBeInTheDocument();
      });
    });

    test('should enable submit button when passwords are valid and match', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<ResetPassword />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByPlaceholderText('New Password');
      const confirmField = screen.getByPlaceholderText('Confirm Password');
      
      await user.type(passwordField, 'ValidPass123!');
      await user.type(confirmField, 'ValidPass123!');

      // Assert
      await waitFor(() => {
        const submitButton = screen.getByRole('button', { name: 'Reset Password' });
        expect(submitButton).not.toBeDisabled();
      });
    });
  });

  describe('API Integration', () => {
    beforeEach(() => {
      mockSearchParams.set('token', 'valid-token-123');
    });

    test('should submit form with valid data', async () => {
      // Arrange
      const user = userEvent.setup();
      mockedAxios.post.mockResolvedValue({
        status: 200,
        data: { success: true, message: 'Password reset successfully' }
      });

      renderWithRouter(<ResetPassword />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByPlaceholderText('New Password');
      const confirmField = screen.getByPlaceholderText('Confirm Password');
      const submitButton = screen.getByRole('button', { name: 'Reset Password' });

      await user.type(passwordField, 'ValidPass123!');
      await user.type(confirmField, 'ValidPass123!');
      await waitFor(() => expect(submitButton).not.toBeDisabled());
      await user.click(submitButton);

      // Assert
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          'http://localhost:8080/api/v1/reset-password',
          expect.objectContaining({
            resetToken: 'valid-token-123',
            newPassword: 'ValidPass123!'
          }),
          expect.objectContaining({
            headers: { "Content-Type": "application/json" },
            validateStatus: expect.any(Function)
          })
        );
      });
    });

    test('should handle API success and redirect', async () => {
      // Arrange
      const user = userEvent.setup();
      mockedAxios.post.mockResolvedValue({
        status: 200,
        data: { success: true, message: 'Password reset successfully' }
      });

      renderWithRouter(<ResetPassword />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByPlaceholderText('New Password');
      const confirmField = screen.getByPlaceholderText('Confirm Password');
      const submitButton = screen.getByRole('button', { name: 'Reset Password' });

      await user.type(passwordField, 'ValidPass123!');
      await user.type(confirmField, 'ValidPass123!');
      await waitFor(() => expect(submitButton).not.toBeDisabled());
      await user.click(submitButton);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Password reset successfully!')).toBeInTheDocument();
      });

      // Wait for redirect
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/signin');
      }, { timeout: 3000 });
    });

    test('should handle API error', async () => {
      // Arrange
      const user = userEvent.setup();
      mockedAxios.post.mockResolvedValue({
        status: 400,
        data: { error: 'Invalid reset token' }
      });

      renderWithRouter(<ResetPassword />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByPlaceholderText('New Password');
      const confirmField = screen.getByPlaceholderText('Confirm Password');
      const submitButton = screen.getByRole('button', { name: 'Reset Password' });

      await user.type(passwordField, 'ValidPass123!');
      await user.type(confirmField, 'ValidPass123!');
      await waitFor(() => expect(submitButton).not.toBeDisabled());
      await user.click(submitButton);

      // Assert
      await waitFor(() => {
        // Accept either thrown error or rendered error alert
        expect(
          screen.queryByText('Failed to reset password. Please try again.') ||
          screen.queryByText('Invalid reset token')
        ).toBeInTheDocument();
      });
    });

    test('should handle network error', async () => {
      // Arrange
      const user = userEvent.setup();
      mockedAxios.post.mockRejectedValue(new Error('Network error'));

      renderWithRouter(<ResetPassword />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
      });

      // Act
      const passwordField = screen.getByPlaceholderText('New Password');
      const confirmField = screen.getByPlaceholderText('Confirm Password');
      const submitButton = screen.getByRole('button', { name: 'Reset Password' });

      await user.type(passwordField, 'ValidPass123!');
      await user.type(confirmField, 'ValidPass123!');
      await waitFor(() => expect(submitButton).not.toBeDisabled());
      await user.click(submitButton);

      // Assert
      await waitFor(() => {
        expect(
          screen.queryByText('Failed to reset password. Please try again.') ||
          screen.queryByText('Network error')
        ).toBeInTheDocument();
      });
    });
  });

  describe('Component Structure', () => {
    beforeEach(() => {
      mockSearchParams.set('token', 'valid-token-123');
    });

    test('should render with proper structure', async () => {
      // Act
      renderWithRouter(<ResetPassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByTestId('paper')).toBeInTheDocument();
        expect(screen.getAllByText('Reset Password')[0]).toBeInTheDocument();
        expect(screen.getByText('Please enter your new password')).toBeInTheDocument();
      });
    });

    test('should have form elements with correct attributes', async () => {
      // Act
      renderWithRouter(<ResetPassword />);

      // Assert
      await waitFor(() => {
        expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Confirm Password')).toBeInTheDocument();
      });
    });

    test('should handle component lifecycle', async () => {
      // Act
      const { unmount } = renderWithRouter(<ResetPassword />);
      await waitFor(() => {
        expect(screen.getAllByText('Reset Password')[0]).toBeInTheDocument();
      });
      // Act - Unmount component
      expect(() => unmount()).not.toThrow();
    });

    test('should disable form when token is invalid', async () => {
      mockSearchParams.delete('token');
      renderWithRouter(<ResetPassword />);
      await waitFor(() => {
        expect(screen.getByText('No reset token provided.')).toBeInTheDocument();
      });
    });
  });

  describe('Button Disabled State', () => {
    beforeEach(() => {
      mockSearchParams.set('token', 'valid-token-123');
    });

    test('should disable submit button when token is loading', async () => {
      // Simulate loading by not resolving token validation
      // (simulate by setting tokenLoading to true via mock)
      // Since tokenLoading is internal, we can simulate by rendering and immediately checking
      renderWithRouter(<ResetPassword />);
      const submitButton = screen.getByRole('button', { name: 'Reset Password' });
      expect(submitButton).toBeDisabled();
    });

    test('should disable submit button when there is a token error', async () => {
      mockSearchParams.delete('token');
      renderWithRouter(<ResetPassword />);
      await waitFor(() => {
        expect(screen.getByText('No reset token provided.')).toBeInTheDocument();
        const submitButton = screen.getByRole('button', { name: 'Reset Password' });
        expect(submitButton).toBeDisabled();
      });
    });

    test('should disable submit button when passwords are empty or invalid', async () => {
      renderWithRouter(<ResetPassword />);
      await waitFor(() => expect(screen.getByPlaceholderText('New Password')).toBeInTheDocument());
      const submitButton = screen.getByRole('button', { name: 'Reset Password' });
      expect(submitButton).toBeDisabled();
    });
  });
});
