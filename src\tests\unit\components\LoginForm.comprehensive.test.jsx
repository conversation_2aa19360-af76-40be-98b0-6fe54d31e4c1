import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import LoginForm from '../../../components/LoginForm';

// Mock AuthContext
const mockLogin = jest.fn();
const mockUseAuth = {
  login: mockLogin,
  isLoading: false,
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth,
}));

// Mock validation messages
jest.mock('../../../constants/validationMessages', () => ({
  SUCCESS_MESSAGES: {
    LOGIN_SUCCESS: 'Login successful'
  },
  ERROR_MESSAGES: {
    INVALID_CREDENTIALS: 'Invalid credentials'
  },
  BUTTON_TEXT: {
    SIGN_IN: 'Sign In'
  },
  FORM_LABELS: {
    EMAIL: 'Email Address',
    AUTH_FIELD: 'Password'
  }
}));

// Mock hooks
const mockFormValidation = {
  values: { email: '', password: '' },
  errors: {},
  handleInputChange: jest.fn(),
  handleBlur: jest.fn(),
  validateForm: jest.fn(() => true),
};

const mockApiCall = {
  execute: jest.fn(),
  loading: false,
  error: null,
};

jest.mock('../../../hooks', () => ({
  useFormValidation: () => mockFormValidation,
  useApiCall: () => mockApiCall,
}));

// Mock common components
jest.mock('../../../components/common', () => ({
  EmailInput: ({ label, value, onChange, onBlur, error, helperText, disabled, sx }) => (
    <div>
      <label>{label}</label>
      <input
        data-testid="email-input"
        type="email"
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        disabled={disabled}
      />
      {error && <span data-testid="email-error">{helperText}</span>}
    </div>
  ),
  PasswordInput: ({ label, value, onChange, onBlur, error, helperText, disabled, sx }) => (
    <div>
      <label>{label}</label>
      <input
        data-testid="password-input"
        type="password"
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        disabled={disabled}
      />
      {error && <span data-testid="password-error">{helperText}</span>}
    </div>
  ),
  SubmitButton: ({ children, disabled, loading, sx }) => (
    <button 
      type="submit" 
      disabled={disabled || loading}
      data-testid="submit-button"
    >
      {loading ? 'Loading...' : children}
    </button>
  ),
  AlertMessage: ({ message, severity, show }) => 
    show ? <div data-testid={`alert-${severity}`}>{message}</div> : null,
  AuthFormHeader: ({ title, subtitle, showLogo }) => (
    <div data-testid="auth-form-header">
      <h1>{title}</h1>
      <p>{subtitle}</p>
    </div>
  ),
  AuthFormFooter: ({ message, linkText, onLinkClick }) => (
    <div data-testid="auth-form-footer">
      <span>{message}</span>
      <button onClick={onLinkClick} data-testid="footer-link">{linkText}</button>
    </div>
  ),
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, component, onSubmit, sx }) => 
    component === 'form' ? 
      <form onSubmit={onSubmit} data-testid="login-form">{children}</form> : 
      <div data-testid="box">{children}</div>,
  Link: ({ children, onClick, component, type, variant, sx }) => 
    <button type={type} onClick={onClick} data-testid="forgot-password-link">{children}</button>,
}));

describe('LoginForm - Comprehensive Tests', () => {
  const mockOnSwitchToSignUp = jest.fn();
  const mockOnSwitchToForgotPassword = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock implementations
    mockFormValidation.values = { email: '', password: '' };
    mockFormValidation.errors = {};
    mockFormValidation.validateForm.mockReturnValue(true);
    mockApiCall.loading = false;
    mockApiCall.error = null;
    mockUseAuth.isLoading = false;
  });

  describe('Initial Render', () => {
    it('renders form with all elements', () => {
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
      expect(screen.getByTestId('auth-form-header')).toBeInTheDocument();
      expect(screen.getByTestId('email-input')).toBeInTheDocument();
      expect(screen.getByTestId('password-input')).toBeInTheDocument();
      expect(screen.getByTestId('forgot-password-link')).toBeInTheDocument();
      expect(screen.getByTestId('submit-button')).toBeInTheDocument();
      expect(screen.getByTestId('auth-form-footer')).toBeInTheDocument();
    });

    it('renders with correct labels and text', () => {
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
      expect(screen.getByText('Sign in to your account to continue')).toBeInTheDocument();
      expect(screen.getByText('Email Address')).toBeInTheDocument();
      expect(screen.getByText('Password')).toBeInTheDocument();
      expect(screen.getByText('Forgot password?')).toBeInTheDocument();
      expect(screen.getByText('Sign In')).toBeInTheDocument();
      expect(screen.getByText('Don\'t have an account?')).toBeInTheDocument();
      expect(screen.getByText('Sign up')).toBeInTheDocument();
    });

    it('has correct input types', () => {
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      expect(screen.getByTestId('email-input')).toHaveAttribute('type', 'email');
      expect(screen.getByTestId('password-input')).toHaveAttribute('type', 'password');
    });
  });

  describe('Form Validation', () => {
    it('calls validateForm on form submission', async () => {
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      const form = screen.getByTestId('login-form');
      fireEvent.submit(form);
      
      expect(mockFormValidation.validateForm).toHaveBeenCalledWith(['email', 'password']);
    });

    it('does not proceed if validation fails', async () => {
      mockFormValidation.validateForm.mockReturnValue(false);
      
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      const form = screen.getByTestId('login-form');
      fireEvent.submit(form);
      
      expect(mockApiCall.execute).not.toHaveBeenCalled();
    });

    it('shows validation errors', () => {
      mockFormValidation.errors = {
        email: 'Email is required',
        password: 'Password is required'
      };
      
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      expect(screen.getByTestId('email-error')).toHaveTextContent('Email is required');
      expect(screen.getByTestId('password-error')).toHaveTextContent('Password is required');
    });
  });

  describe('Form Submission', () => {
    it('handles successful form submission', async () => {
      mockFormValidation.values = { email: '<EMAIL>', password: 'password123' };
      
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      const form = screen.getByTestId('login-form');
      fireEvent.submit(form);
      
      expect(mockApiCall.execute).toHaveBeenCalledWith(
        expect.any(Function),
        {
          successMessage: 'Login successful',
          errorMessage: 'Invalid credentials'
        }
      );
    });

    it('calls login with correct credentials', async () => {
      mockFormValidation.values = { email: '<EMAIL>', password: 'password123' };
      
      // Mock the execute function to actually call the login function
      mockApiCall.execute.mockImplementation(async (loginCall) => {
        await loginCall();
      });
      
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      const form = screen.getByTestId('login-form');
      fireEvent.submit(form);
      
      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
      });
    });

    it('shows loading state during submission', () => {
      mockApiCall.loading = true;
      
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(screen.getByTestId('submit-button')).toBeDisabled();
    });

    it('disables form during auth loading', () => {
      mockUseAuth.isLoading = true;
      
      render(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      
      expect(screen.getByTestId('email-input')).toBeDisabled();
      expect(screen.getByTestId('password-input')).toBeDisabled();
      expect(screen.getByTestId('submit-button')).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('shows API error message', () => {
      mockApiCall.error = 'Invalid credentials';

      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      expect(screen.getByTestId('alert-error')).toHaveTextContent('Invalid credentials');
    });

    it('does not show error message when no error', () => {
      mockApiCall.error = null;

      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      expect(screen.queryByTestId('alert-error')).not.toBeInTheDocument();
    });
  });

  describe('Input Handling', () => {
    it('handles email input change', () => {
      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const emailInput = screen.getByTestId('email-input');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(mockFormValidation.handleInputChange).toHaveBeenCalledWith('email', '<EMAIL>');
    });

    it('handles password input change', () => {
      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const passwordInput = screen.getByTestId('password-input');
      fireEvent.change(passwordInput, { target: { value: 'password123' } });

      expect(mockFormValidation.handleInputChange).toHaveBeenCalledWith('password', 'password123');
    });

    it('handles email input blur', () => {
      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const emailInput = screen.getByTestId('email-input');
      fireEvent.blur(emailInput);

      expect(mockFormValidation.handleBlur).toHaveBeenCalledWith('email');
    });

    it('handles password input blur', () => {
      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const passwordInput = screen.getByTestId('password-input');
      fireEvent.blur(passwordInput);

      expect(mockFormValidation.handleBlur).toHaveBeenCalledWith('password');
    });
  });

  describe('Navigation', () => {
    it('calls onSwitchToSignUp when sign up link is clicked', () => {
      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const signUpLink = screen.getByTestId('footer-link');
      fireEvent.click(signUpLink);

      expect(mockOnSwitchToSignUp).toHaveBeenCalled();
    });

    it('calls onSwitchToForgotPassword when forgot password link is clicked', () => {
      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const forgotPasswordLink = screen.getByTestId('forgot-password-link');
      fireEvent.click(forgotPasswordLink);

      expect(mockOnSwitchToForgotPassword).toHaveBeenCalled();
    });

    it('handles multiple clicks on navigation links', () => {
      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const signUpLink = screen.getByTestId('footer-link');
      const forgotPasswordLink = screen.getByTestId('forgot-password-link');

      fireEvent.click(signUpLink);
      fireEvent.click(forgotPasswordLink);
      fireEvent.click(signUpLink);

      expect(mockOnSwitchToSignUp).toHaveBeenCalledTimes(2);
      expect(mockOnSwitchToForgotPassword).toHaveBeenCalledTimes(1);
    });
  });

  describe('Component Integration', () => {
    it('passes correct props to EmailInput', () => {
      mockFormValidation.values = { email: '<EMAIL>', password: 'password123' };
      mockFormValidation.errors = { email: 'Invalid email' };
      mockUseAuth.isLoading = true;

      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const emailInput = screen.getByTestId('email-input');
      expect(emailInput).toHaveValue('<EMAIL>');
      expect(emailInput).toBeDisabled();
      expect(screen.getByTestId('email-error')).toHaveTextContent('Invalid email');
    });

    it('passes correct props to PasswordInput', () => {
      mockFormValidation.values = { email: '<EMAIL>', password: 'password123' };
      mockFormValidation.errors = { password: 'Password too short' };
      mockUseAuth.isLoading = true;

      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const passwordInput = screen.getByTestId('password-input');
      expect(passwordInput).toHaveValue('password123');
      expect(passwordInput).toBeDisabled();
      expect(screen.getByTestId('password-error')).toHaveTextContent('Password too short');
    });

    it('passes correct props to SubmitButton', () => {
      mockApiCall.loading = true;
      mockUseAuth.isLoading = true;

      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const submitButton = screen.getByTestId('submit-button');
      expect(submitButton).toBeDisabled();
      expect(submitButton).toHaveTextContent('Loading...');
    });

    it('passes correct props to AuthFormHeader', () => {
      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
      expect(screen.getByText('Sign in to your account to continue')).toBeInTheDocument();
    });

    it('passes correct props to AuthFormFooter', () => {
      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      expect(screen.getByText('Don\'t have an account?')).toBeInTheDocument();
      expect(screen.getByText('Sign up')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles form submission with empty values', () => {
      mockFormValidation.values = { email: '', password: '' };
      mockFormValidation.validateForm.mockReturnValue(false);

      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const form = screen.getByTestId('login-form');
      fireEvent.submit(form);

      expect(mockApiCall.execute).not.toHaveBeenCalled();
    });

    it('handles rapid form submissions', () => {
      mockApiCall.loading = true;

      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const form = screen.getByTestId('login-form');
      fireEvent.submit(form);
      fireEvent.submit(form);

      // Button should be disabled during loading
      expect(screen.getByTestId('submit-button')).toBeDisabled();
    });

    it('handles both loading states simultaneously', () => {
      mockApiCall.loading = true;
      mockUseAuth.isLoading = true;

      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      expect(screen.getByTestId('email-input')).toBeDisabled();
      expect(screen.getByTestId('password-input')).toBeDisabled();
      expect(screen.getByTestId('submit-button')).toBeDisabled();
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('handles login function returning success', async () => {
      mockFormValidation.values = { email: '<EMAIL>', password: 'password123' };

      // Mock the execute function to call the login function and return success
      mockApiCall.execute.mockImplementation(async (loginCall) => {
        const result = await loginCall();
        expect(result).toEqual({ success: true });
      });

      render(
        <LoginForm
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );

      const form = screen.getByTestId('login-form');
      fireEvent.submit(form);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
      });
    });
  });
});
