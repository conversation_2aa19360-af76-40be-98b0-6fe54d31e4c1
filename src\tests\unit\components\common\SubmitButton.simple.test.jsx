import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import SubmitButton from '../../../../components/common/SubmitButton';

// Mock MUI components
jest.mock('@mui/material', () => ({
  Button: ({ children, onClick, disabled, variant, color, fullWidth, type, sx, loading, error, ...props }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      data-testid="submit-button"
      data-variant={variant}
      data-fullwidth={fullWidth?.toString()}
      data-color={color}
      type={type}
    >
      {children}
    </button>
  ),
  CircularProgress: ({ size, color }) => (
    <div data-testid="loading-spinner" data-size={size} data-color={color}>
      Loading...
    </div>
  ),
}));

describe('SubmitButton - Simple Tests', () => {
  const mockOnClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders submit button', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit</SubmitButton>);
      
      expect(screen.getByTestId('submit-button')).toBeInTheDocument();
    });

    it('displays button text', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit Form</SubmitButton>);
      
      expect(screen.getByText('Submit Form')).toBeInTheDocument();
    });

    it('renders with default props', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toHaveAttribute('data-variant', 'contained');
      expect(button).toHaveAttribute('data-fullwidth', 'true');
      expect(button).toHaveAttribute('type', 'submit');
    });

    it('renders with custom variant', () => {
      render(<SubmitButton onClick={mockOnClick} variant="outlined">Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toHaveAttribute('data-variant', 'outlined');
    });
  });

  describe('Loading State', () => {
    it('shows loading spinner when loading', () => {
      render(<SubmitButton onClick={mockOnClick} loading={true}>Submit</SubmitButton>);
      
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('disables button when loading', () => {
      render(<SubmitButton onClick={mockOnClick} loading={true}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toBeDisabled();
    });

    it('does not show spinner when not loading', () => {
      render(<SubmitButton onClick={mockOnClick} loading={false}>Submit</SubmitButton>);
      
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    it('shows button text when not loading', () => {
      render(<SubmitButton onClick={mockOnClick} loading={false}>Submit</SubmitButton>);
      
      expect(screen.getByText('Submit')).toBeInTheDocument();
    });

    it('hides button text when loading', () => {
      render(<SubmitButton onClick={mockOnClick} loading={true}>Submit</SubmitButton>);
      
      expect(screen.queryByText('Submit')).not.toBeInTheDocument();
    });
  });

  describe('Disabled State', () => {
    it('disables button when disabled prop is true', () => {
      render(<SubmitButton onClick={mockOnClick} disabled={true}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toBeDisabled();
    });

    it('enables button when disabled prop is false', () => {
      render(<SubmitButton onClick={mockOnClick} disabled={false}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).not.toBeDisabled();
    });

    it('disables button when both loading and disabled', () => {
      render(<SubmitButton onClick={mockOnClick} loading={true} disabled={true}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toBeDisabled();
    });
  });

  describe('User Interactions', () => {
    it('calls onClick when button is clicked', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      fireEvent.click(button);
      
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('does not call onClick when disabled', () => {
      render(<SubmitButton onClick={mockOnClick} disabled={true}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      fireEvent.click(button);
      
      expect(mockOnClick).not.toHaveBeenCalled();
    });

    it('does not call onClick when loading', () => {
      render(<SubmitButton onClick={mockOnClick} loading={true}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      fireEvent.click(button);
      
      expect(mockOnClick).not.toHaveBeenCalled();
    });

    it('handles multiple clicks', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);
      
      expect(mockOnClick).toHaveBeenCalledTimes(3);
    });
  });

  describe('Button Types', () => {
    it('renders with submit type by default', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toHaveAttribute('type', 'submit');
    });

    it('renders with custom type', () => {
      render(<SubmitButton onClick={mockOnClick} type="button">Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toHaveAttribute('type', 'button');
    });

    it('renders with reset type', () => {
      render(<SubmitButton onClick={mockOnClick} type="reset">Reset</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toHaveAttribute('type', 'reset');
    });
  });

  describe('Full Width', () => {
    it('renders full width by default', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toHaveAttribute('data-fullwidth', 'true');
    });

    it('renders without full width when specified', () => {
      render(<SubmitButton onClick={mockOnClick} fullWidth={false}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toHaveAttribute('data-fullwidth', 'false');
    });
  });

  describe('Error Handling', () => {
    it('renders without errors', () => {
      expect(() => {
        render(<SubmitButton onClick={mockOnClick}>Submit</SubmitButton>);
      }).not.toThrow();
    });

    it('handles missing onClick gracefully', () => {
      expect(() => {
        render(<SubmitButton>Submit</SubmitButton>);
      }).not.toThrow();
    });

    it('handles missing children gracefully', () => {
      expect(() => {
        render(<SubmitButton onClick={mockOnClick} />);
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('is accessible via keyboard', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button).toBeInTheDocument();
      
      button.focus();
      expect(document.activeElement).toBe(button);
    });

    it('has proper button semantics', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit</SubmitButton>);
      
      const button = screen.getByTestId('submit-button');
      expect(button.tagName).toBe('BUTTON');
    });

    it('provides loading state to screen readers', () => {
      render(<SubmitButton onClick={mockOnClick} loading={true}>Submit</SubmitButton>);
      
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });
  });

  describe('Common Use Cases', () => {
    it('renders form submit button', () => {
      render(<SubmitButton onClick={mockOnClick}>Submit Form</SubmitButton>);
      
      expect(screen.getByText('Submit Form')).toBeInTheDocument();
      expect(screen.getByTestId('submit-button')).toHaveAttribute('type', 'submit');
    });

    it('renders loading submit button', () => {
      render(<SubmitButton onClick={mockOnClick} loading={true}>Submitting...</SubmitButton>);
      
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      expect(screen.getByTestId('submit-button')).toBeDisabled();
    });

    it('renders disabled submit button', () => {
      render(<SubmitButton onClick={mockOnClick} disabled={true}>Submit</SubmitButton>);
      
      expect(screen.getByTestId('submit-button')).toBeDisabled();
      expect(screen.getByText('Submit')).toBeInTheDocument();
    });
  });
});
