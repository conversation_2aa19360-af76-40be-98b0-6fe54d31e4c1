import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONFIG_PATH = path.join(__dirname, '..', 'public', 'runtime-config.json');

function checkRuntimeConfig() {
  console.log('🔍 Checking runtime configuration...');
  
  if (!fs.existsSync(CONFIG_PATH)) {
    console.log('⚠️  Runtime config not found at:', CONFIG_PATH);
    console.log('📝 Creating default runtime config...');
    
    const defaultConfig = {
      currentBackend: 'spring',
      backendUrl: 'http://localhost:8080',
      environment: 'development',
      apiVersion: 'v1'
    };
    
    fs.writeFileSync(CONFIG_PATH, JSON.stringify(defaultConfig, null, 2));
    console.log('✅ Default runtime config created');
    return;
  }
  
  try {
    const config = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
    console.log('✅ Runtime config found:');
    console.log('   Backend:', config.currentBackend);
    console.log('   URL:', config.backendUrl);
    console.log('   Environment:', config.environment);
  } catch (error) {
    console.error('❌ Invalid runtime config JSON:', error.message);
    process.exit(1);
  }
}

// Always run when executed directly
checkRuntimeConfig();

export { checkRuntimeConfig };
