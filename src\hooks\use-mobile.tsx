import * as React from "react"

const M<PERSON><PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }

    // Check if addEventListener is available (for older browsers or test environments)
    if (mql.addEventListener) {
      mql.addEventListener("change", onChange)
    }

    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)

    return () => {
      if (mql.removeEventListener) {
        mql.removeEventListener("change", onChange)
      }
    }
  }, [])

  return !!isMobile
}
