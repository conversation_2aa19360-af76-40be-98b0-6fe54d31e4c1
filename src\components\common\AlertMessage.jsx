import React from 'react';
import { Alert } from '@mui/material';
import PropTypes from 'prop-types';

const AlertMessage = ({
  message,
  severity = "error",
  show = true,
  sx = {},
  ...props
}) => {
  if (!show || !message) return null;

  return (
    <Alert 
      severity={severity} 
      sx={{ 
        mb: 2, 
        textAlign: 'center',
        ...sx 
      }}
      {...props}
    >
      {message}
    </Alert>
  );
};

AlertMessage.propTypes = {
  message: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  severity: PropTypes.oneOf(['error', 'warning', 'info', 'success']),
  show: PropTypes.bool,
  sx: PropTypes.object,
};

export default AlertMessage;
