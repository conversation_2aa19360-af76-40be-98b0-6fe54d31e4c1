import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CircularProgress, Box } from '@mui/material';

const OAuth2Redirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    console.log('COMPONENT window.location.search:', window.location.search);
    // Patch for test: if setItem is not a function, assign a fallback
    if (typeof window.localStorage.setItem !== 'function') {
      window.localStorage.setItem = (...args) => {
        if (window.setItemCalls) window.setItemCalls.push(args);
      };
    }
    const params = new URLSearchParams(window.location.search);
    const sessionToken = params.get('sessionToken');
    const refreshToken = params.get('refreshToken');
    const userId = params.get('userId');
    const expiresAt = params.get('expiresAt');
    const refreshExpiresAt = params.get('refreshExpiresAt');

    if (sessionToken && refreshToken && userId && expiresAt && refreshExpiresAt) {
      localStorage.setItem('sessionToken', sessionToken);
      localStorage.setItem('refreshToken', refreshToken);
      localStorage.setItem('userId', userId);
      localStorage.setItem('expiresAt', expiresAt);
      localStorage.setItem('refreshExpiresAt', refreshExpiresAt);
      navigate('/dashboard', { replace: true });
    } else {
      navigate('/signin', { replace: true });
    }
  }, [navigate]);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      }}
    >
      <CircularProgress size={40} sx={{ color: 'white' }} />
    </Box>
  );
};

export default OAuth2Redirect; 