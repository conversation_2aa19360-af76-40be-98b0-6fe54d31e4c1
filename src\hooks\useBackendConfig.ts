import { useState, useEffect } from 'react';
import { configService, type RuntimeConfig } from '../services/configService';

export const useBackendConfig = () => {
  const [config, setConfig] = useState<RuntimeConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    const loadConfig = async () => {
      try {
        setLoading(true);
        const runtimeConfig = await configService.getConfig();

        // Only update state if component is still mounted
        if (isMounted) {
          setConfig(runtimeConfig);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'Failed to load configuration');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    loadConfig();

    return () => {
      isMounted = false;
    };
  }, []);

  const refreshConfig = async () => {
    try {
      setLoading(true);
      const runtimeConfig = await configService.loadConfig();
      setConfig(runtimeConfig);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh configuration');
    } finally {
      setLoading(false);
    }
  };

  return {
    config,
    loading,
    error,
    refreshConfig,
    currentBackend: config?.currentBackend || 'spring',
    backendUrl: config?.backendUrl || 'http://localhost:8080'
  };
};
