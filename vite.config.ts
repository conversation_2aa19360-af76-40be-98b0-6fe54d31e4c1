import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on mode
  const env = loadEnv(mode, process.cwd(), '');

  // Handle new environment file structure
  const envFile = mode.includes('.') ? mode : `${mode}`;

  return {
    server: {
      host: "::",
      port: 3000,
    },
    plugins: [
      react(),
      mode === 'development' && componentTagger(),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    define: {
      __APP_ENV__: JSON.stringify(env.VITE_APP_ENV || 'dev'),
      __SERVICE_NAME__: JSON.stringify(env.VITE_APP_SERVICE_NAME || ''),
      __BACKEND_NAMESPACE__: JSON.stringify(env.VITE_APP_BACKEND_NAMESPACE || ''),
    },
  };
});
