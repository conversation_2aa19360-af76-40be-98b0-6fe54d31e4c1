import React from 'react';
import { render, screen } from '@testing-library/react';

// Mock GoogleLoginButton to avoid import.meta issues
jest.mock('../../../../components/GoogleLoginButton', () => {
  return function GoogleLoginButton() {
    return <div>GoogleLoginButton</div>;
  };
});

import { AuthFormHeader } from '../../../../components/common';

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, sx, ...props }) => (
    <div data-testid="header-box" {...props}>{children}</div>
  ),
  Typography: ({ children, variant, component, align, sx, gutterBottom, ...props }) => (
    <div
      data-testid={`typography-${variant || 'default'}`}
      data-component={component}
      data-align={align}
      data-gutterbottom={gutterBottom}
      {...props}
    >
      {children}
    </div>
  ),
  Avatar: ({ sx, ...props }) => (
    <div data-testid="avatar" {...props}>
      <span data-testid="lock-icon">🔒</span>
    </div>
  ),
}));

// Mock MUI icons
jest.mock('@mui/icons-material/LockOutlined', () => {
  return function LockOutlinedIcon() {
    return <span data-testid="lock-outlined-icon">🔒</span>;
  };
}, { virtual: true });

describe('AuthFormHeader', () => {
  describe('Component Rendering', () => {
    it('renders the header container', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" />);

      expect(screen.getByTestId('typography-h5')).toBeInTheDocument();
    });

    it('displays the title', () => {
      const title = "Sign In";
      render(<AuthFormHeader title={title} subtitle="Welcome back" />);

      expect(screen.getByText(title)).toBeInTheDocument();
    });

    it('displays the subtitle when provided', () => {
      const subtitle = "Welcome back";
      render(<AuthFormHeader title="Sign In" subtitle={subtitle} />);

      expect(screen.getByText(subtitle)).toBeInTheDocument();
    });

    it('renders title with correct typography variant', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" />);

      expect(screen.getByTestId('typography-h5')).toBeInTheDocument();
    });

    it('renders subtitle with correct typography variant when provided', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" />);

      expect(screen.getByTestId('typography-body2')).toBeInTheDocument();
    });

    it('renders logo when showLogo is true', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" showLogo={true} />);

      expect(screen.getByAltText('Brand Logo')).toBeInTheDocument();
    });

    it('does not render logo when showLogo is false', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" showLogo={false} />);

      expect(screen.queryByAltText('Brand Logo')).not.toBeInTheDocument();
    });

    it('renders Google login by default', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" />);

      expect(screen.getByText('GoogleLoginButton')).toBeInTheDocument();
    });
  });

  describe('Component Props', () => {
    it('accepts custom title prop', () => {
      const customTitle = "Create Account";
      render(<AuthFormHeader title={customTitle} subtitle="Join us today" />);

      expect(screen.getByText(customTitle)).toBeInTheDocument();
    });

    it('accepts custom subtitle prop', () => {
      const customSubtitle = "Join us today";
      render(<AuthFormHeader title="Sign Up" subtitle={customSubtitle} />);

      expect(screen.getByText(customSubtitle)).toBeInTheDocument();
    });

    it('handles empty title', () => {
      render(<AuthFormHeader title="" subtitle="Test subtitle" />);

      expect(screen.queryByTestId('typography-h5')).not.toBeInTheDocument();
      expect(screen.getByTestId('typography-body2')).toBeInTheDocument();
    });

    it('handles empty subtitle', () => {
      render(<AuthFormHeader title="Test Title" subtitle="" />);

      expect(screen.getByTestId('typography-h5')).toBeInTheDocument();
      expect(screen.queryByTestId('typography-body2')).not.toBeInTheDocument();
    });

    it('renders without errors when required props are provided', () => {
      expect(() => {
        render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" />);
      }).not.toThrow();
    });
  });

  describe('Logo Display', () => {
    it('shows logo when showLogo is explicitly true', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" showLogo={true} />);

      expect(screen.getByAltText('Brand Logo')).toBeInTheDocument();
    });

    it('hides logo when showLogo is explicitly false', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" showLogo={false} />);

      expect(screen.queryByAltText('Brand Logo')).not.toBeInTheDocument();
    });

    it('shows logo by default', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" />);

      expect(screen.getByAltText('Brand Logo')).toBeInTheDocument();
    });

    it('handles showLogo with truthy values', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" showLogo={true} />);

      expect(screen.getByAltText('Brand Logo')).toBeInTheDocument();
    });

    it('handles showLogo with falsy values', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" showLogo={false} />);

      expect(screen.queryByAltText('Brand Logo')).not.toBeInTheDocument();
    });
  });

  describe('Typography Alignment', () => {
    it('centers title text', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" />);

      const titleElement = screen.getByTestId('typography-h5');
      expect(titleElement).toBeInTheDocument();
    });

    it('centers subtitle text when provided', () => {
      render(<AuthFormHeader title="Test Title" subtitle="Test Subtitle" />);

      const subtitleElement = screen.getByTestId('typography-body2');
      expect(subtitleElement).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('renders without errors when only title is provided', () => {
      expect(() => {
        render(<AuthFormHeader title="Test Title" />);
      }).not.toThrow();
    });

    it('renders without errors when all props are provided', () => {
      expect(() => {
        render(
          <AuthFormHeader 
            title="Test Title" 
            subtitle="Test Subtitle" 
            showLogo={true} 
          />
        );
      }).not.toThrow();
    });

    it('renders without errors when no props are provided', () => {
      expect(() => {
        render(<AuthFormHeader />);
      }).not.toThrow();
    });

    it('handles special characters in title', () => {
      const specialTitle = "Sign In & Welcome! 🎉";
      render(<AuthFormHeader title={specialTitle} />);
      
      expect(screen.getByText(specialTitle)).toBeInTheDocument();
    });

    it('handles special characters in subtitle', () => {
      const specialSubtitle = "Welcome back! 👋 Let's get started...";
      render(<AuthFormHeader title="Sign In" subtitle={specialSubtitle} />);
      
      expect(screen.getByText(specialSubtitle)).toBeInTheDocument();
    });
  });

  describe('Common Use Cases', () => {
    it('renders sign in header correctly', () => {
      render(
        <AuthFormHeader
          title="Sign In"
          subtitle="Welcome back to your account"
          showLogo={true}
        />
      );

      expect(screen.getByText("Sign In")).toBeInTheDocument();
      expect(screen.getByText("Welcome back to your account")).toBeInTheDocument();
      expect(screen.getByAltText('Brand Logo')).toBeInTheDocument();
    });

    it('renders sign up header correctly', () => {
      render(
        <AuthFormHeader
          title="Create Account"
          subtitle="Join us today and get started"
          showLogo={false}
        />
      );

      expect(screen.getByText("Create Account")).toBeInTheDocument();
      expect(screen.getByText("Join us today and get started")).toBeInTheDocument();
      expect(screen.queryByAltText('Brand Logo')).not.toBeInTheDocument();
    });

    it('renders forgot password header correctly', () => {
      render(
        <AuthFormHeader 
          title="Reset Password" 
          subtitle="Enter your email to receive reset instructions" 
        />
      );
      
      expect(screen.getByText("Reset Password")).toBeInTheDocument();
      expect(screen.getByText("Enter your email to receive reset instructions")).toBeInTheDocument();
    });

    it('renders minimal header with title only', () => {
      render(<AuthFormHeader title="Welcome" />);
      
      expect(screen.getByText("Welcome")).toBeInTheDocument();
      expect(screen.queryByTestId('typography-body1')).not.toBeInTheDocument();
    });

    it('handles long title text', () => {
      const longTitle = "This is a very long title that might wrap to multiple lines";
      render(<AuthFormHeader title={longTitle} />);
      
      expect(screen.getByText(longTitle)).toBeInTheDocument();
    });

    it('handles long subtitle text', () => {
      const longSubtitle = "This is a very long subtitle that provides detailed information about what the user should do next";
      render(<AuthFormHeader title="Short Title" subtitle={longSubtitle} />);
      
      expect(screen.getByText(longSubtitle)).toBeInTheDocument();
    });
  });
});
