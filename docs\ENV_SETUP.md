# Environment Setup

This project uses environment variables to configure API endpoints and other settings for different backends (Spring Boot, Nest.js, Django) and environments (local, beta, production).

## Generating Environment Files

To generate the required .env files, run:

```sh
npm run generate:envs
```

This will create the following files in your project root:
- `.env.spring`
- `.env.beta.spring`
- `.env.production.spring`
- `.env.nest`
- `.env.beta.nest`
- `.env.production.nest`
- `.env.django`
- `.env.beta.django`
- `.env.production.django`

## Example .env File (Spring Boot)
```env
VITE_APP_ENV=local
VITE_APP_API_URL=http://localhost:8080
VITE_APP_PORT=3000
VITE_APP_GOOGLE_OAUTH_URL=http://localhost:8080/oauth2/authorize/google?redirect_uri=http://localhost:3000/oauth2/redirect

## Example .env File (Nest.js)
```env
VITE_APP_ENV=local
VITE_APP_API_URL=http://localhost:3001
VITE_APP_PORT=3000
VITE_APP_GOOGLE_OAUTH_URL=http://localhost:8080/oauth2/authorize/google?redirect_uri=http://localhost:3000/oauth2/redirect
```

## Example .env File (Django)
```env
VITE_APP_ENV=local
VITE_APP_API_URL=http://localhost:8000
VITE_APP_PORT=3000
VITE_APP_GOOGLE_OAUTH_URL=http://localhost:8080/oauth2/authorize/google?redirect_uri=http://localhost:3000/oauth2/redirect
```

## Customizing Environment Variables

Edit the generated .env files to match your backend server URLs and any other required settings.

**Note:** All environment variables used in the frontend must be prefixed with `VITE_` to be accessible in the React app. 