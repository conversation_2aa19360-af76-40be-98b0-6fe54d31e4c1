// scripts/generate-env-files.js
import fs from 'fs';
import path from 'path';

// Environment configurations based on new structure
const environments = {
  // Local development environments
  'spring': { port: 8080, env: 'local' },
  'nest': { port: 8080, env: 'local' },
  'django': { port: 8000, env: 'local' },

  // Development environments
  'dev.spring': {
    port: 8080,
    env: 'dev',
    serviceName: 'ai-spring-backend-service',
    namespace: 'ai-spring-backend-dev',
    externalUrl: 'http://*************:8080',
    frontendUrl: 'http://*************:3000'
  },
  'dev.nest': {
    port: 8080,
    env: 'dev',
    serviceName: 'ai-nest-backend-service',
    namespace: 'ai-nest-backend-dev',
    externalUrl: 'http://*************:8080',
    frontendUrl: 'http://*************:3000'
  },
  'dev.django': {
    port: 8000,
    env: 'dev',
    serviceName: 'ai-django-backend-service',
    namespace: 'ai-django-backend-dev',
    externalUrl: 'http://*************:8000',
    frontendUrl: 'http://*************:3000'
  },

  // Staging environments
  'staging.spring': {
    port: 8080,
    env: 'staging',
    serviceName: 'ai-spring-backend-service',
    namespace: 'ai-spring-backend-staging',
    useInternalService: true
  },
  'staging.nest': {
    port: 8080,
    env: 'staging',
    serviceName: 'ai-nest-backend-service',
    namespace: 'ai-nest-backend-staging',
    useInternalService: true
  },
  'staging.django': {
    port: 8000,
    env: 'staging',
    serviceName: 'ai-django-backend-service',
    namespace: 'ai-django-backend-staging',
    useInternalService: true
  },

  // Production environments
  'prod.spring': {
    port: 8080,
    env: 'prod',
    serviceName: 'ai-spring-backend-service',
    namespace: 'ai-spring-backend-prod',
    useInternalService: true
  },
  'prod.nest': {
    port: 8080,
    env: 'prod',
    serviceName: 'ai-nest-backend-service',
    namespace: 'ai-nest-backend-prod',
    useInternalService: true
  },
  'prod.django': {
    port: 8000,
    env: 'prod',
    serviceName: 'ai-django-backend-service',
    namespace: 'ai-django-backend-prod',
    useInternalService: true
  },
};

// Generate environment files
Object.entries(environments).forEach(([mode, config]) => {
  const backendType = mode.includes('.') ? mode.split('.')[1] : mode;
  const frontendPort = 3000;

  // Determine API URL based on environment
  let apiUrl;
  let oauthUrl;
  let frontendUrl;

  if (config.useInternalService) {
    // Use Kubernetes internal service communication
    apiUrl = `http://${config.serviceName}.${config.namespace}.svc.cluster.local:${config.port}`;
    frontendUrl = `http://ai-react-frontend-service.ai-react-frontend-${config.env}.svc.cluster.local:${frontendPort}`;
    oauthUrl = `${apiUrl}/oauth2/authorize/google?redirect_uri=${frontendUrl}/oauth2/redirect`;
  } else if (config.externalUrl) {
    // Use external URLs for dev environment
    apiUrl = config.externalUrl;
    frontendUrl = config.frontendUrl;
    oauthUrl = `${apiUrl}/oauth2/authorize/google?redirect_uri=${frontendUrl}/oauth2/redirect`;
  } else {
    // Use localhost for local development
    apiUrl = `http://localhost:${config.port}`;
    frontendUrl = `http://localhost:${frontendPort}`;
    oauthUrl = `${apiUrl}/oauth2/authorize/google?redirect_uri=${frontendUrl}/oauth2/redirect`;
  }

  let envContent = `VITE_APP_ENV=${config.env}
VITE_APP_API_URL=${apiUrl}
VITE_APP_PORT=${frontendPort}
VITE_APP_GOOGLE_OAUTH_URL=${oauthUrl}`;

  // Add service-specific variables for non-local environments
  if (config.serviceName && config.namespace) {
    envContent += `
VITE_APP_SERVICE_NAME=${config.serviceName}
VITE_APP_BACKEND_NAMESPACE=${config.namespace}`;
  }

  envContent += '\n';

  const fileName = path.join(process.cwd(), `.env.${mode}`);
  fs.writeFileSync(fileName, envContent);
  console.log(`✅ Created ${fileName}`);
});
