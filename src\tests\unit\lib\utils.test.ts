import { cn } from '@/lib/utils';

describe('cn utility function', () => {
  it('combines class names correctly', () => {
    const result = cn('class1', 'class2');
    expect(result).toBe('class1 class2');
  });

  it('handles conditional classes', () => {
    const result = cn('base-class', true && 'conditional-class', false && 'hidden-class');
    expect(result).toBe('base-class conditional-class');
  });

  it('merges Tailwind classes correctly', () => {
    // twMerge should handle conflicting Tailwind classes
    const result = cn('p-4', 'p-2');
    expect(result).toBe('p-2'); // Later class should override
  });

  it('handles arrays of classes', () => {
    const result = cn(['class1', 'class2'], 'class3');
    expect(result).toBe('class1 class2 class3');
  });

  it('handles objects with boolean values', () => {
    const result = cn({
      'active': true,
      'disabled': false,
      'primary': true
    });
    expect(result).toBe('active primary');
  });

  it('handles undefined and null values', () => {
    const result = cn('class1', undefined, null, 'class2');
    expect(result).toBe('class1 class2');
  });

  it('handles empty input', () => {
    const result = cn();
    expect(result).toBe('');
  });

  it('handles complex mixed inputs', () => {
    const result = cn(
      'base',
      ['array1', 'array2'],
      { 'conditional': true, 'hidden': false },
      undefined,
      'final'
    );
    expect(result).toBe('base array1 array2 conditional final');
  });
});
