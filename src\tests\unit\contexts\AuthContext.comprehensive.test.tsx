import React from 'react';
import { render, screen, waitFor, act, fireEvent } from '@testing-library/react';
import axios from 'axios';
import { AuthProvider, useAuth } from '../../../contexts/AuthContext';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock environment config
jest.mock('../../../config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:3000',
}));

// Test component to use all auth methods
const TestComponent = () => {
  const { 
    user, 
    isLoading, 
    login, 
    signUp, 
    loginWithGoogle, 
    logout, 
    forgotPassword, 
    resetPassword, 
    setAuthenticatedUser 
  } = useAuth();
  
  const [error, setError] = React.useState<string>('');

  const handleLogin = async () => {
    try {
      setError('');
      await login('<EMAIL>', 'password');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Login failed');
    }
  };

  const handleSignUp = async () => {
    try {
      setError('');
      await signUp('Test User', '<EMAIL>', 'password');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Sign up failed');
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setError('');
      await loginWithGoogle();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Google login failed');
    }
  };

  const handleForgotPassword = async () => {
    try {
      setError('');
      await forgotPassword('<EMAIL>');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Forgot password failed');
    }
  };

  const handleResetPassword = async () => {
    try {
      setError('');
      await resetPassword('token123', 'newpassword');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Reset password failed');
    }
  };

  const handleSetUser = () => {
    setAuthenticatedUser({
      id: '123',
      email: '<EMAIL>',
      name: 'Set User',
      profilePicture: 'https://example.com/pic.jpg'
    });
  };
  
  return (
    <div>
      <div data-testid="user">{user ? user.email : 'No user'}</div>
      <div data-testid="user-name">{user ? user.name : 'No name'}</div>
      <div data-testid="user-id">{user ? user.id : 'No id'}</div>
      <div data-testid="user-picture">{user?.profilePicture || 'No picture'}</div>
      <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
      <div data-testid="error">{error}</div>
      <button onClick={handleLogin} data-testid="login-btn">Login</button>
      <button onClick={handleSignUp} data-testid="signup-btn">Sign Up</button>
      <button onClick={handleGoogleLogin} data-testid="google-btn">Google Login</button>
      <button onClick={logout} data-testid="logout-btn">Logout</button>
      <button onClick={handleForgotPassword} data-testid="forgot-btn">Forgot Password</button>
      <button onClick={handleResetPassword} data-testid="reset-btn">Reset Password</button>
      <button onClick={handleSetUser} data-testid="set-user-btn">Set User</button>
    </div>
  );
};

describe('AuthContext - Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    // Mock IP address API
    mockedAxios.get.mockResolvedValue({ data: { ip: '***********' } });
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe('Provider and Hook', () => {
    it('provides initial state', () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
      
      expect(screen.getByTestId('user')).toHaveTextContent('No user');
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });

    it('throws error when useAuth is used outside provider', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        render(<TestComponent />);
      }).toThrow('useAuth must be used within an AuthProvider');
      
      consoleSpy.mockRestore();
    });
  });

  describe('Initial Authentication Check', () => {
    it('loads existing user from localStorage on mount', () => {
      localStorage.setItem('userId', '123');
      localStorage.setItem('sessionToken', 'token123');
      localStorage.setItem('userEmail', '<EMAIL>');
      localStorage.setItem('userName', 'Stored User');
      localStorage.setItem('userProfilePicture', 'https://example.com/stored.jpg');

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('user-name')).toHaveTextContent('Stored User');
      expect(screen.getByTestId('user-id')).toHaveTextContent('123');
      expect(screen.getByTestId('user-picture')).toHaveTextContent('https://example.com/stored.jpg');
    });

    it('loads user without profile picture from localStorage', () => {
      localStorage.setItem('userId', '789');
      localStorage.setItem('sessionToken', 'token789');
      localStorage.setItem('userEmail', '<EMAIL>');
      localStorage.setItem('userName', 'No Pic User');

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('user-picture')).toHaveTextContent('No picture');
    });

    it('does not load user when userId missing', () => {
      localStorage.setItem('sessionToken', 'token123');
      localStorage.setItem('userEmail', '<EMAIL>');

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(screen.getByTestId('user')).toHaveTextContent('No user');
    });

    it('does not load user when sessionToken missing', () => {
      localStorage.setItem('userId', '123');
      localStorage.setItem('userEmail', '<EMAIL>');

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(screen.getByTestId('user')).toHaveTextContent('No user');
    });
  });

  describe('setAuthenticatedUser', () => {
    it('sets user and stores in localStorage with profile picture', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('set-user-btn'));
      });

      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('user-name')).toHaveTextContent('Set User');
      expect(screen.getByTestId('user-id')).toHaveTextContent('123');
      expect(screen.getByTestId('user-picture')).toHaveTextContent('https://example.com/pic.jpg');

      expect(localStorage.getItem('userId')).toBe('123');
      expect(localStorage.getItem('userEmail')).toBe('<EMAIL>');
      expect(localStorage.getItem('userName')).toBe('Set User');
      expect(localStorage.getItem('userProfilePicture')).toBe('https://example.com/pic.jpg');
    });

    it('sets user without profile picture', async () => {
      const TestComponentNoPic = () => {
        const { setAuthenticatedUser, user } = useAuth();
        
        const handleSetUser = () => {
          setAuthenticatedUser({
            id: '456',
            email: '<EMAIL>',
            name: 'No Pic User'
          });
        };
        
        return (
          <div>
            <div data-testid="user">{user ? user.email : 'No user'}</div>
            <button onClick={handleSetUser} data-testid="set-user-btn">Set User</button>
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestComponentNoPic />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('set-user-btn'));
      });

      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      expect(localStorage.getItem('userProfilePicture')).toBeNull();
    });
  });

  describe('Login', () => {
    it('handles successful login with 200 status', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 200,
        data: {
          userId: '123',
          sessionToken: 'token123',
          refreshToken: 'refresh123',
          expiresAt: '2024-12-31',
          refreshExpiresAt: '2025-01-31'
        }
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('login-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
        expect(screen.getByTestId('user-name')).toHaveTextContent('test');
      });

      expect(localStorage.getItem('userId')).toBe('123');
      expect(localStorage.getItem('sessionToken')).toBe('token123');
      expect(localStorage.getItem('userEmail')).toBe('<EMAIL>');
    });

    it('handles successful login with 201 status', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 201,
        data: {
          userId: '456',
          sessionToken: 'token456',
          refreshToken: 'refresh456',
          expiresAt: '2024-12-31',
          refreshExpiresAt: '2025-01-31'
        }
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('login-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      });
    });

    it('handles login failure with error response', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 401,
        data: { error: 'Invalid credentials' }
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('login-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Invalid credentials or connection error');
      });
    });

    it('handles login network error', async () => {
      mockedAxios.post.mockRejectedValueOnce(new Error('Network error'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('login-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Invalid credentials or connection error');
      });
    });

    it('handles IP address fetch failure during login', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('IP fetch failed'));
      mockedAxios.post.mockResolvedValueOnce({
        status: 200,
        data: {
          userId: '123',
          sessionToken: 'token123',
          refreshToken: 'refresh123',
          expiresAt: '2024-12-31',
          refreshExpiresAt: '2025-01-31'
        }
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('login-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      });

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/login',
        expect.objectContaining({
          ipAddress: '',
          email: '<EMAIL>',
          password: 'password',
          deviceDetails: expect.any(String),
          overrideExistingLogins: true
        }),
        expect.any(Object)
      );
    });

    it('includes device details in login request', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 200,
        data: {
          userId: '123',
          sessionToken: 'token123',
          refreshToken: 'refresh123',
          expiresAt: '2024-12-31',
          refreshExpiresAt: '2025-01-31'
        }
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('login-btn'));
      });

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:3000/api/v1/login',
        expect.objectContaining({
          deviceDetails: navigator.userAgent
        }),
        expect.any(Object)
      );
    });

    it('shows loading state during login', async () => {
      let resolveLogin: (value: any) => void;
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve;
      });

      mockedAxios.post.mockReturnValueOnce(loginPromise as any);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('login-btn'));
      });

      expect(screen.getByTestId('loading')).toHaveTextContent('Loading');

      await act(async () => {
        resolveLogin!({
          status: 200,
          data: {
            userId: '123',
            sessionToken: 'token123',
            refreshToken: 'refresh123',
            expiresAt: '2024-12-31',
            refreshExpiresAt: '2025-01-31'
          }
        });
      });

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      });
    });
  });

  describe('SignUp', () => {
    it('handles sign up without setting user', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('signup-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      }, { timeout: 3000 });

      expect(screen.getByTestId('user')).toHaveTextContent('No user');
    });

    it('shows loading state during sign up', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('signup-btn'));
      });

      expect(screen.getByTestId('loading')).toHaveTextContent('Loading');

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      }, { timeout: 3000 });
    });

    it('handles sign up error', async () => {
      const TestComponentWithError = () => {
        const { signUp, isLoading } = useAuth();
        const [error, setError] = React.useState<string>('');

        const handleSignUp = async () => {
          try {
            setError('');
            // Force an error by rejecting the promise
            jest.spyOn(global, 'setTimeout').mockImplementationOnce((callback: any) => {
              throw new Error('Registration failed');
            });
            await signUp('Test User', '<EMAIL>', 'password');
          } catch (error) {
            setError(error instanceof Error ? error.message : 'Sign up failed');
          }
        };

        return (
          <div>
            <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
            <div data-testid="error">{error}</div>
            <button onClick={handleSignUp} data-testid="signup-btn">Sign Up</button>
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestComponentWithError />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('signup-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Registration failed');
      });
    });
  });

  describe('Google Login', () => {
    it('handles successful Google login', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('google-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
        expect(screen.getByTestId('user-name')).toHaveTextContent('Google User');
        expect(screen.getByTestId('user-id')).toHaveTextContent('google_1');
        expect(screen.getByTestId('user-picture')).toHaveTextContent('https://via.placeholder.com/40');
      }, { timeout: 2000 });
    });

    it('shows loading state during Google login', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('google-btn'));
      });

      expect(screen.getByTestId('loading')).toHaveTextContent('Loading');

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      }, { timeout: 2000 });
    });

    it('handles Google login error', async () => {
      const TestComponentWithError = () => {
        const { loginWithGoogle, isLoading } = useAuth();
        const [error, setError] = React.useState<string>('');

        const handleGoogleLogin = async () => {
          try {
            setError('');
            jest.spyOn(global, 'setTimeout').mockImplementationOnce((callback: any) => {
              throw new Error('Google authentication failed');
            });
            await loginWithGoogle();
          } catch (error) {
            setError(error instanceof Error ? error.message : 'Google login failed');
          }
        };

        return (
          <div>
            <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
            <div data-testid="error">{error}</div>
            <button onClick={handleGoogleLogin} data-testid="google-btn">Google Login</button>
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestComponentWithError />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('google-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Google authentication failed');
      });
    });
  });

  describe('Logout', () => {
    it('clears user and localStorage on logout', async () => {
      localStorage.setItem('userId', '123');
      localStorage.setItem('sessionToken', 'token123');
      localStorage.setItem('refreshToken', 'refresh123');
      localStorage.setItem('expiresAt', '2024-12-31');
      localStorage.setItem('refreshExpiresAt', '2025-01-31');
      localStorage.setItem('userEmail', '<EMAIL>');
      localStorage.setItem('userName', 'Test User');
      localStorage.setItem('userProfilePicture', 'https://example.com/pic.jpg');

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');

      await act(async () => {
        fireEvent.click(screen.getByTestId('logout-btn'));
      });

      expect(screen.getByTestId('user')).toHaveTextContent('No user');
      expect(localStorage.getItem('userId')).toBeNull();
      expect(localStorage.getItem('sessionToken')).toBeNull();
      expect(localStorage.getItem('refreshToken')).toBeNull();
      expect(localStorage.getItem('expiresAt')).toBeNull();
      expect(localStorage.getItem('refreshExpiresAt')).toBeNull();
      expect(localStorage.getItem('userEmail')).toBeNull();
      expect(localStorage.getItem('userName')).toBeNull();
      expect(localStorage.getItem('userProfilePicture')).toBeNull();
    });

    it('handles logout when no user is logged in', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(screen.getByTestId('user')).toHaveTextContent('No user');

      await act(async () => {
        fireEvent.click(screen.getByTestId('logout-btn'));
      });

      expect(screen.getByTestId('user')).toHaveTextContent('No user');
    });
  });

  describe('Forgot Password', () => {
    it('handles forgot password request', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('forgot-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      }, { timeout: 2000 });
    });

    it('shows loading state during forgot password', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('forgot-btn'));
      });

      expect(screen.getByTestId('loading')).toHaveTextContent('Loading');

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      }, { timeout: 2000 });
    });

    it('handles forgot password error', async () => {
      const TestComponentWithError = () => {
        const { forgotPassword, isLoading } = useAuth();
        const [error, setError] = React.useState<string>('');

        const handleForgotPassword = async () => {
          try {
            setError('');
            jest.spyOn(global, 'setTimeout').mockImplementationOnce((callback: any) => {
              throw new Error('Failed to send reset email');
            });
            await forgotPassword('<EMAIL>');
          } catch (error) {
            setError(error instanceof Error ? error.message : 'Forgot password failed');
          }
        };

        return (
          <div>
            <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
            <div data-testid="error">{error}</div>
            <button onClick={handleForgotPassword} data-testid="forgot-btn">Forgot Password</button>
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestComponentWithError />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('forgot-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Failed to send reset email');
      });
    });
  });

  describe('Reset Password', () => {
    it('handles reset password request', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('reset-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      }, { timeout: 2000 });
    });

    it('shows loading state during reset password', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('reset-btn'));
      });

      expect(screen.getByTestId('loading')).toHaveTextContent('Loading');

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      }, { timeout: 2000 });
    });

    it('handles reset password error', async () => {
      const TestComponentWithError = () => {
        const { resetPassword, isLoading } = useAuth();
        const [error, setError] = React.useState<string>('');

        const handleResetPassword = async () => {
          try {
            setError('');
            jest.spyOn(global, 'setTimeout').mockImplementationOnce((callback: any) => {
              throw new Error('Failed to reset password');
            });
            await resetPassword('token123', 'newpassword');
          } catch (error) {
            setError(error instanceof Error ? error.message : 'Reset password failed');
          }
        };

        return (
          <div>
            <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
            <div data-testid="error">{error}</div>
            <button onClick={handleResetPassword} data-testid="reset-btn">Reset Password</button>
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestComponentWithError />
        </AuthProvider>
      );

      await act(async () => {
        fireEvent.click(screen.getByTestId('reset-btn'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Failed to reset password');
      });
    });
  });
});
