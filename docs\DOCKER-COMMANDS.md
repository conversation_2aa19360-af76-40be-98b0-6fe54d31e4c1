# Common Docker Commands for Managing Containers, Images, and Volumes

---

## 0. Ensuring Your New Code is in the Docker Image

Whenever you make changes to your code, follow these steps to ensure your Docker image includes the latest code:

### 1. Rebuild the Docker Image
```
docker build -t my-react-app .
```
- If you suspect Dock<PERSON> is using cache and not picking up changes, use:
```
docker build --no-cache -t my-react-app .
```

### 2. Run the New Image
```
docker run -d -p 3000:80 --name Spring-AI my-react-app
```

### 3. Test Your App
- Open [http://localhost:3000](http://localhost:3000) in your browser and verify your new changes are present.

### 4. (Optional) Check the Image Creation Time
```
docker images
```
- The `CREATED` column should show a recent timestamp for your image.

### 5. (Optional) Inspect the Container's Filesystem
```
docker run -it --rm my-react-app sh
# Then check /usr/share/nginx/html or your build output directory
```

---

## 1. Build the Docker Image
```
docker build -t my-react-app .
```

---

## 2. Start (Run) the Container
```
docker run -d -p 3000:80 --name Spring-AI my-react-app
```
- `-d` runs in detached mode
- `-p 3000:80` maps port 3000 on host to 80 in container
- `--name Spring-AI` names the container

---

## 3. Stop the Container
```
docker stop Spring-AI
```

---

## 4. Start an Existing (Stopped) Container
```
docker start Spring-AI
```
- Use this to restart a container that was previously stopped.

---

## 5. Remove the Container
```
docker rm Spring-AI
```

---

## 6. Remove the Image
```
docker rmi my-react-app
```

---

## 7. List All Containers
```
docker ps -a
```

---

## 8. List All Images
```
docker images
```

---

## 9. Using Volumes (Optional)
If you want to persist data or mount a local directory into the container, use the `-v` flag:

```
docker run -d -p 8080:80 --name Spring-AI -v /path/on/host:/path/in/container my-react-app
```
- Example for Nginx static files:
  - Mount a local `dist` folder to Nginx html directory:
    ```
    docker run -d -p 8080:80 --name Spring-AI -v $(pwd)/dist:/usr/share/nginx/html my-react-app
    ```

---

## 10. Tag and Push to Docker Hub (Example)
```
docker tag my-react-app shashank/my-react-app:latest
docker push shashank/my-react-app:latest
```

---

## 11. List All Volumes
```
docker volume ls
```

---

## 12. Remove a Volume
```
docker volume rm <volume_name>
```

---

## 13. Prune (Remove) All Unused Data
```
docker system prune -a
```

---

## 14. View Container Logs
```
docker logs Spring-AI
``` 