import React from 'react';
import {
  TextField,
  InputAdornment,
} from '@mui/material';
import { Mail } from 'lucide-react';
import PropTypes from 'prop-types';

const EmailInput = ({
  label = "Email Address",
  value,
  onChange,
  onBlur,
  error,
  helperText,
  disabled = false,
  autoComplete = "email",
  showStartAdornment = true,
  placeholder,
  size = "small",
  sx = {},
  ...props
}) => {
  return (
    <TextField
      fullWidth
      label={label}
      type="email"
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      error={error}
      helperText={helperText}
      disabled={disabled}
      autoComplete={autoComplete}
      placeholder={placeholder}
      size={size}
      InputProps={{
        startAdornment: showStartAdornment && (
          <InputAdornment position="start">
            <Mail size={18} color="#666" />
          </InputAdornment>
        ),
      }}
      sx={{
        '& .MuiOutlinedInput-root': {
          borderRadius: 1.5,
        },
        ...sx,
      }}
      {...props}
    />
  );
};

EmailInput.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  onBlur: PropTypes.func,
  error: PropTypes.bool,
  helperText: PropTypes.string,
  disabled: PropTypes.bool,
  autoComplete: PropTypes.string,
  showStartAdornment: PropTypes.bool,
  placeholder: PropTypes.string,
  size: PropTypes.string,
  sx: PropTypes.object,
};

export default EmailInput;
