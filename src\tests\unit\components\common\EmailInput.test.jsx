import React from 'react';
import { render, screen } from '@testing-library/react';
import EmailInput from '@/components/common/EmailInput';

jest.mock('@mui/material', () => ({
  TextField: jest.fn(({ InputProps, fullWidth, label, type, value, onChange, onBlur, error, helperText, disabled, autoComplete, placeholder, size, sx, loading, ...props }) => {
    // Filter out non-DOM props but keep data-* and aria-* attributes
    const domProps = Object.keys(props).reduce((acc, key) => {
      if (key.startsWith('data-') || key.startsWith('aria-')) {
        acc[key] = props[key];
      }
      return acc;
    }, {});

    return (
      <div
        data-testid="mui-textfield"
        data-fullwidth={fullWidth}
        data-label={label}
        data-type={type}
        data-error={error}
        data-disabled={disabled}
        data-size={size}
        {...domProps}
      >
        {InputProps?.startAdornment}
        {helperText && <span data-testid="helper-text">{helperText}</span>}
      </div>
    );
  }),
  InputAdornment: jest.fn(({ children, position, ...props }) => <span data-testid={`adornment-${position}`}>{children}</span>),
}));
jest.mock('lucide-react', () => ({
  Mail: () => <span data-testid="mail-icon">mail</span>,
}));

describe('EmailInput', () => {
  const requiredProps = {
    value: '',
    onChange: jest.fn(),
  };

  it('renders with required props', () => {
    render(<EmailInput {...requiredProps} />);
    expect(screen.getByTestId('mui-textfield')).toBeInTheDocument();
  });

  it('shows mail icon when showStartAdornment is true', () => {
    render(<EmailInput {...requiredProps} showStartAdornment={true} />);
    expect(screen.getByTestId('adornment-start')).toContainElement(screen.getByTestId('mail-icon'));
  });

  it('does not show mail icon when showStartAdornment is false', () => {
    render(<EmailInput {...requiredProps} showStartAdornment={false} />);
    expect(screen.queryByTestId('adornment-start')).toBeNull();
  });

  it('forwards extra props to TextField', () => {
    render(<EmailInput {...requiredProps} data-custom="foo" />);
    expect(screen.getByTestId('mui-textfield')).toHaveAttribute('data-custom', 'foo');
  });

  it('renders with helperText and error', () => {
    const { TextField } = require('@mui/material');
    render(<EmailInput {...requiredProps} helperText="Error!" error={true} />);
    const lastCall = TextField.mock.calls[TextField.mock.calls.length - 1][0];
    expect(lastCall.helperText).toBe('Error!');
    expect(lastCall.error).toBe(true);
  });

  it('renders with custom label and placeholder', () => {
    render(<EmailInput {...requiredProps} label="Custom Email" placeholder="Enter email" />);
    const { TextField } = require('@mui/material');
    const lastCall = TextField.mock.calls[TextField.mock.calls.length - 1][0];
    expect(lastCall.label).toBe('Custom Email');
    expect(lastCall.placeholder).toBe('Enter email');
  });
}); 