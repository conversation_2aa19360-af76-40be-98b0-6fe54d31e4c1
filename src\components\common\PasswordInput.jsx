import React from 'react';
import PropTypes from 'prop-types';
import BasePasswordInput from './BasePasswordInput';

/**
 * Password input component for login and authentication forms
 * Uses current-password autoComplete by default
 */
const PasswordInput = ({
  autoComplete = "current-password",
  ...props
}) => {
  return (
    <BasePasswordInput
      autoComplete={autoComplete}
      {...props}
    />
  );
};

PasswordInput.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  onBlur: PropTypes.func,
  error: PropTypes.bool,
  helperText: PropTypes.string,
  disabled: PropTypes.bool,
  autoComplete: PropTypes.string,
  showStartAdornment: PropTypes.bool,
  placeholder: PropTypes.string,
  size: PropTypes.string,
  sx: PropTypes.object,
};

export default PasswordInput;
