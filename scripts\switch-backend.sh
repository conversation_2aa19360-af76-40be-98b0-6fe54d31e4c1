#!/bin/bash

# Backend Switching Script for Dev Environment
# This script allows easy switching between Spring, Django, and Nest backends
# in the dev environment using the new runtime configuration approach

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="ai-react-frontend-dev"
CONFIGMAP_NAME="ai-react-frontend-env"
DEPLOYMENT_NAME="ai-react-frontend"

# Backend configurations
declare -A BACKENDS
BACKENDS[spring]="http://*************:8080|ai-spring-backend-service|ai-spring-backend-dev"
BACKENDS[django]="http://*************:8000|ai-django-backend-service|ai-django-backend-dev"
BACKENDS[nest]="http://**************:3000|ai-nest-backend-service|ai-nest-backend-dev"

# Function to display usage
usage() {
    echo -e "${BLUE}🔄 Backend Switching Script${NC}"
    echo ""
    echo "Usage: $0 [backend]"
    echo ""
    echo "Available backends:"
    echo "  spring  - Switch to Spring Boot backend (http://*************:8080)"
    echo "  django  - Switch to Django backend (http://*************:8000)"
    echo "  nest    - Switch to Nest backend (http://**************:3000)"
    echo "  status  - Show current backend configuration"
    echo ""
    echo "Examples:"
    echo "  $0 spring   # Switch to Spring backend"
    echo "  $0 django   # Switch to Django backend"
    echo "  $0 nest     # Switch to Nest backend"
    echo "  $0 status   # Show current configuration"
    echo ""
}

# Function to show current backend status
show_status() {
    echo -e "${BLUE}📋 Current Backend Configuration:${NC}"
    echo ""
    
    if kubectl get configmap $CONFIGMAP_NAME -n $NAMESPACE >/dev/null 2>&1; then
        kubectl get configmap $CONFIGMAP_NAME -n $NAMESPACE -o jsonpath='{.data.env-config\.js}' | \
        grep -E "(REACT_APP_BACKEND_URL|REACT_APP_CURRENT_BACKEND)" | \
        sed 's/.*REACT_APP_BACKEND_URL: "\([^"]*\)".*/Backend URL: \1/' | \
        sed 's/.*REACT_APP_CURRENT_BACKEND: "\([^"]*\)".*/Backend Type: \1/'
        
        echo ""
        echo -e "${YELLOW}🔍 Testing backend health...${NC}"
        
        CURRENT_URL=$(kubectl get configmap $CONFIGMAP_NAME -n $NAMESPACE -o jsonpath='{.data.env-config\.js}' | \
                     grep -o 'REACT_APP_BACKEND_URL: "[^"]*"' | \
                     sed 's/.*: "\([^"]*\)".*/\1/')
        
        BACKEND_TYPE=$(kubectl get configmap $CONFIGMAP_NAME -n $NAMESPACE -o jsonpath='{.data.env-config\.js}' | \
                      grep -o 'REACT_APP_CURRENT_BACKEND: "[^"]*"' | \
                      sed 's/.*: "\([^"]*\)".*/\1/')
        
        case $BACKEND_TYPE in
            "spring")
                if curl -f -s "$CURRENT_URL/actuator/health" >/dev/null 2>&1; then
                    echo -e "${GREEN}✅ Spring Boot backend is healthy${NC}"
                else
                    echo -e "${RED}❌ Spring Boot backend is not responding${NC}"
                fi
                ;;
            "django")
                if curl -f -s "$CURRENT_URL/health" >/dev/null 2>&1; then
                    echo -e "${GREEN}✅ Django backend is healthy${NC}"
                else
                    echo -e "${RED}❌ Django backend is not responding${NC}"
                fi
                ;;
            "nest")
                if curl -f -s "$CURRENT_URL/health" >/dev/null 2>&1; then
                    echo -e "${GREEN}✅ Nest backend is healthy${NC}"
                else
                    echo -e "${RED}❌ Nest backend is not responding${NC}"
                fi
                ;;
        esac
    else
        echo -e "${RED}❌ ConfigMap $CONFIGMAP_NAME not found in namespace $NAMESPACE${NC}"
        exit 1
    fi
}

# Function to switch backend
switch_backend() {
    local backend=$1
    
    if [[ ! ${BACKENDS[$backend]+_} ]]; then
        echo -e "${RED}❌ Invalid backend: $backend${NC}"
        usage
        exit 1
    fi
    
    # Parse backend configuration
    IFS='|' read -r url service_name namespace <<< "${BACKENDS[$backend]}"
    
    echo -e "${BLUE}🔄 Switching to $backend backend...${NC}"
    echo "   URL: $url"
    echo "   Service: $service_name"
    echo "   Namespace: $namespace"
    echo ""
    
    # Create the env-config.js content
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    local oauth_url="$url/oauth2/authorize/google?redirect_uri=http://*************:3000/oauth2/redirect"
    
    local env_config="window._env_ = {
  REACT_APP_BACKEND_URL: \"$url\",
  REACT_APP_CURRENT_BACKEND: \"$backend\",
  REACT_APP_ENVIRONMENT: \"dev\",
  REACT_APP_API_VERSION: \"v1\",
  REACT_APP_SERVICE_NAME: \"$service_name\",
  REACT_APP_BACKEND_NAMESPACE: \"$namespace\",
  REACT_APP_GOOGLE_OAUTH_URL: \"$oauth_url\",
  REACT_APP_USE_RUNTIME_CONFIG: \"true\",
  REACT_APP_DEBUG_MODE: \"true\",
  REACT_APP_LAST_UPDATED: \"$timestamp\"
};"
    
    # Update ConfigMap
    echo -e "${YELLOW}📝 Updating ConfigMap...${NC}"
    kubectl patch configmap $CONFIGMAP_NAME -n $NAMESPACE --type merge -p "{
        \"data\": {
            \"env-config.js\": \"$env_config\"
        }
    }"
    
    # Restart deployment
    echo -e "${YELLOW}🔄 Restarting deployment...${NC}"
    kubectl rollout restart deployment $DEPLOYMENT_NAME -n $NAMESPACE
    
    # Wait for rollout to complete
    echo -e "${YELLOW}⏳ Waiting for deployment to complete...${NC}"
    kubectl rollout status deployment $DEPLOYMENT_NAME -n $NAMESPACE --timeout=300s
    
    echo ""
    echo -e "${GREEN}✅ Successfully switched to $backend backend!${NC}"
    echo -e "${GREEN}🌐 Backend URL: $url${NC}"
    echo -e "${GREEN}⚙️  Service: $service_name${NC}"
    echo ""
    echo -e "${BLUE}🔧 To verify the switch:${NC}"
    echo "   1. Check frontend: http://*************:3000"
    echo "   2. Open browser dev tools and check window._env_"
    echo "   3. Run: $0 status"
}

# Main script logic
if [[ $# -eq 0 ]]; then
    usage
    exit 1
fi

case $1 in
    spring|django|nest)
        switch_backend $1
        ;;
    status)
        show_status
        ;;
    -h|--help|help)
        usage
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo ""
        usage
        exit 1
        ;;
esac
