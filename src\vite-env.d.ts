/// <reference types="vite/client" />

// Runtime Environment Configuration Types
interface RuntimeEnvironment {
  // Backend Configuration
  REACT_APP_BACKEND_URL: string;
  REACT_APP_CURRENT_BACKEND: 'spring' | 'django' | 'nest';

  // Environment Configuration
  REACT_APP_ENVIRONMENT: 'development' | 'dev' | 'staging' | 'prod';
  REACT_APP_API_VERSION: string;

  // Service Configuration (for Kubernetes environments)
  REACT_APP_SERVICE_NAME?: string;
  REACT_APP_BACKEND_NAMESPACE?: string;

  // OAuth Configuration
  REACT_APP_GOOGLE_OAUTH_URL?: string;

  // Additional Configuration
  REACT_APP_PORT?: string;
  REACT_APP_LAST_UPDATED?: string;

  // Feature Flags
  REACT_APP_USE_RUNTIME_CONFIG?: string;
  REACT_APP_DEBUG_MODE?: string;
}

// Global Window Interface Extension
declare global {
  interface Window {
    _env_: RuntimeEnvironment;
  }
}

// Export the interface for use in other files
export type { RuntimeEnvironment };
