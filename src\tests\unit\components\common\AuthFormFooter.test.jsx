import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';

// Mock GoogleLoginButton to avoid import.meta issues
jest.mock('../../../../components/GoogleLoginButton', () => {
  return function GoogleLoginButton() {
    return <div>GoogleLoginButton</div>;
  };
});

import { AuthFormFooter } from '../../../../components/common';

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, sx, ...props }) => (
    <div data-testid="footer-box" {...props}>{children}</div>
  ),
  Typography: ({ children, variant, component, sx, ...props }) => (
    <span 
      data-testid={`typography-${variant || 'default'}`}
      data-component={component}
      {...props}
    >
      {children}
    </span>
  ),
  Link: ({ children, onClick, sx, ...props }) => (
    <button 
      data-testid="footer-link"
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  ),
}));

describe('AuthFormFooter', () => {
  const mockOnLinkClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders the footer container', () => {
      render(
        <AuthFormFooter
          message="Test message"
          linkText="Test link"
          onLinkClick={mockOnLinkClick}
        />
      );

      expect(screen.getByTestId('typography-body2')).toBeInTheDocument();
    });

    it('displays the message text', () => {
      const message = "Don't have an account?";
      render(
        <AuthFormFooter 
          message={message} 
          linkText="Sign up" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByText(message)).toBeInTheDocument();
    });

    it('displays the link text', () => {
      const linkText = "Sign up";
      render(
        <AuthFormFooter 
          message="Don't have an account?" 
          linkText={linkText} 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByText(linkText)).toBeInTheDocument();
    });

    it('renders message with correct typography variant', () => {
      render(
        <AuthFormFooter 
          message="Test message" 
          linkText="Test link" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByTestId('typography-body2')).toBeInTheDocument();
    });

    it('renders link as clickable element', () => {
      render(
        <AuthFormFooter 
          message="Test message" 
          linkText="Test link" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByTestId('footer-link')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls onLinkClick when link is clicked', () => {
      render(
        <AuthFormFooter 
          message="Test message" 
          linkText="Test link" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      const link = screen.getByTestId('footer-link');
      fireEvent.click(link);
      
      expect(mockOnLinkClick).toHaveBeenCalledTimes(1);
    });

    it('handles multiple link clicks', () => {
      render(
        <AuthFormFooter 
          message="Test message" 
          linkText="Test link" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      const link = screen.getByTestId('footer-link');
      fireEvent.click(link);
      fireEvent.click(link);
      fireEvent.click(link);
      
      expect(mockOnLinkClick).toHaveBeenCalledTimes(3);
    });

    it('handles missing onLinkClick prop gracefully', () => {
      expect(() => {
        render(
          <AuthFormFooter 
            message="Test message" 
            linkText="Test link" 
          />
        );
      }).not.toThrow();
    });

    it('does not crash when link is clicked without onLinkClick prop', () => {
      render(
        <AuthFormFooter 
          message="Test message" 
          linkText="Test link" 
        />
      );
      
      const link = screen.getByTestId('footer-link');
      expect(() => {
        fireEvent.click(link);
      }).not.toThrow();
    });
  });

  describe('Component Props', () => {
    it('accepts custom message prop', () => {
      const customMessage = "Already have an account?";
      render(
        <AuthFormFooter 
          message={customMessage} 
          linkText="Sign in" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByText(customMessage)).toBeInTheDocument();
    });

    it('accepts custom linkText prop', () => {
      const customLinkText = "Create account";
      render(
        <AuthFormFooter 
          message="Don't have an account?" 
          linkText={customLinkText} 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByText(customLinkText)).toBeInTheDocument();
    });

    it('accepts custom onLinkClick handler', () => {
      const customHandler = jest.fn();
      render(
        <AuthFormFooter 
          message="Test message" 
          linkText="Test link" 
          onLinkClick={customHandler} 
        />
      );
      
      const link = screen.getByTestId('footer-link');
      fireEvent.click(link);
      
      expect(customHandler).toHaveBeenCalledTimes(1);
    });

    it('renders with empty message', () => {
      render(
        <AuthFormFooter
          message=""
          linkText="Test link"
          onLinkClick={mockOnLinkClick}
        />
      );

      expect(screen.getByTestId('typography-body2')).toBeInTheDocument();
    });

    it('renders with empty linkText', () => {
      render(
        <AuthFormFooter
          message="Test message"
          linkText=""
          onLinkClick={mockOnLinkClick}
        />
      );

      expect(screen.getByTestId('typography-body2')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('link is accessible via keyboard', () => {
      render(
        <AuthFormFooter 
          message="Test message" 
          linkText="Test link" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      const link = screen.getByTestId('footer-link');
      expect(link).toBeInTheDocument();
      
      // Link should be focusable
      link.focus();
      expect(document.activeElement).toBe(link);
    });

    it('has proper button semantics for link', () => {
      render(
        <AuthFormFooter 
          message="Test message" 
          linkText="Test link" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      const link = screen.getByTestId('footer-link');
      expect(link.tagName).toBe('BUTTON');
    });

    it('message has proper text semantics', () => {
      render(
        <AuthFormFooter 
          message="Test message" 
          linkText="Test link" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      const message = screen.getByTestId('typography-body2');
      expect(message.tagName).toBe('SPAN');
    });
  });

  describe('Error Handling', () => {
    it('handles onLinkClick calls correctly', () => {
      const clickHandler = jest.fn();

      render(
        <AuthFormFooter
          message="Test message"
          linkText="Test link"
          onLinkClick={clickHandler}
        />
      );

      const link = screen.getByTestId('footer-link');
      fireEvent.click(link);

      expect(clickHandler).toHaveBeenCalledTimes(1);
    });

    it('renders correctly with null props', () => {
      expect(() => {
        render(
          <AuthFormFooter 
            message={null} 
            linkText={null} 
            onLinkClick={null} 
          />
        );
      }).not.toThrow();
    });

    it('renders correctly with undefined props', () => {
      expect(() => {
        render(
          <AuthFormFooter 
            message={undefined} 
            linkText={undefined} 
            onLinkClick={undefined} 
          />
        );
      }).not.toThrow();
    });
  });

  describe('Common Use Cases', () => {
    it('renders sign up footer correctly', () => {
      render(
        <AuthFormFooter 
          message="Don't have an account?" 
          linkText="Sign up" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByText("Don't have an account?")).toBeInTheDocument();
      expect(screen.getByText("Sign up")).toBeInTheDocument();
    });

    it('renders sign in footer correctly', () => {
      render(
        <AuthFormFooter 
          message="Already have an account?" 
          linkText="Sign in" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByText("Already have an account?")).toBeInTheDocument();
      expect(screen.getByText("Sign in")).toBeInTheDocument();
    });

    it('handles long message text', () => {
      const longMessage = "This is a very long message that might wrap to multiple lines in the footer component";
      render(
        <AuthFormFooter 
          message={longMessage} 
          linkText="Click here" 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByText(longMessage)).toBeInTheDocument();
    });

    it('handles long link text', () => {
      const longLinkText = "This is a very long link text that might be unusual";
      render(
        <AuthFormFooter 
          message="Short message" 
          linkText={longLinkText} 
          onLinkClick={mockOnLinkClick} 
        />
      );
      
      expect(screen.getByText(longLinkText)).toBeInTheDocument();
    });
  });
});
